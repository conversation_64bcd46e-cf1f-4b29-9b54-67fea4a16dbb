<?php ?>
@extends('layouts.app',['sidebar_display'=>false])
@section('content')
    <!-- Image and text -->
    <nav class="navbar navbar-light bg-light border-bottom">
        <a class="navbar-brand text-dark Poppins bold" href="https://apimio.com/">
            <img src="{{asset('media/sidebar/apimio-small.svg')}}" width="30" height="30" class="d-inline-block align-top" alt="" loading="lazy">
            Apimio
        </a>
    </nav>

    <div class="container mt-3 ">
        <a href="{{url('admin101')}}" class="btn btn-outline-primary mt-3 mb-3 pt-1">
            <i class="flaticon flaticon-arrow-previous icon-size"></i>
        </a>
        <div class="card border-radius shadow-none mb-4">
            <div class="card-body p-0">
                <div class="row">
                    <div class="col-12 col-sm-12 col-md-8 col-lg-8 col-xl-9">
                        <div class="mt-2">
                            <h3 class="Poppins semibold m-0 text-dark">User Organizations</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{--Search form--}}
        {{--        <div class="row mb-3">--}}
        {{--            <div class="col-12 col-md-12 col-lg-12 col-xl-3">--}}
        {{--                <form action="" class="filter-form float-xl-right">--}}
        {{--                    <div class="input-group mb-3">--}}
        {{--                        <input type="search" class="form-control" value="{{request('q')}}" name="q"--}}
        {{--                               placeholder="Search by Email"--}}
        {{--                               aria-label="Search" aria-describedby="search">--}}
        {{--                        <div class="input-group-append">--}}
        {{--                            <button class="btn btn-dark search" type="submit" id="search">--}}
        {{--                                <img src="{{asset('media/retailer-dashboard/Search-2.png')}}" alt="">--}}
        {{--                            </button>--}}
        {{--                        </div>--}}
        {{--                    </div>--}}
        {{--                </form>--}}
        {{--            </div>--}}
        {{--        </div>--}}

        <div class="table-responsive">
            <table class="table">
                <tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Region</th>
                    <th>Units</th>
                    <th>Currency</th>
                    <th>Total Files</th>
                    <th>File Size</th>
                    <th>Status</th>
                    <th>Subscribed</th>
                    @if($_SERVER['PHP_AUTH_USER'] == 'devs' || $_SERVER['PHP_AUTH_PW'] == 'Apimiodevs123')
                        <th scope="col">Actions</th>
                    @endif
                </tr>
                @foreach($organizations['org'] as $organization)
                    <tr>
                        <td>{{$loop->iteration}}</td>
                        <td>{{$organization->name}}</td>
                        <td>{{$organization->region}}</td>
                        <td>{{$organization->units}}</td>
                        <td>{{$organization->currency}}</td>
                        <td>{{$organization->files_count}}</td>
                        <td>{{$organization->files_size}} kb's <b>/</b> {{round($organization->files_size/1024)}} mb's</td>
                        <td>{{$organization->block_status == 0 ? 'active':'blocked'}}</td>
                        <td>{{$organization->stripe_id ? 'subscribed':'not subscribed'}}</td>
                        @if($_SERVER['PHP_AUTH_USER'] == 'devs' || $_SERVER['PHP_AUTH_PW'] == 'Apimiodevs123')
                            <td>
                                <a href="{{route('delete.organization',$organization->id)}}" class="btn btn-outline-danger">Delete Organization</a>
                            </td>
                        @endif
                    </tr>
                @endforeach
            </table>
        </div>

    </div>
@endsection
