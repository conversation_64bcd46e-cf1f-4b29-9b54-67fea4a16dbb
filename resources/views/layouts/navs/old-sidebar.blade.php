{{--
    @deprecated This file is deprecated and will be removed in the future.
--}}



{{--Side bar toggle button--}}

<div class="icon-menu" id="burger-icon-div">
    <a href="#" class="list-group-item list-group-item-action border-0 icon-menu" style="width: 74px">
        <img src="{{asset('media/sidebar/burger-icon-2.png')}}" alt="">
    </a>
</div>

<div class="list-group list-group-flush sidebar-width" id="sidebar-width" style="z-index: 10;">

    <!--SPACE-->
    <div class="list-group-item list-group-item-action border-0 p-05 disabled"></div>

    {{--Side bar close button--}}
    <button type="button" id="close" class="close my-1" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>

    {{--Apimio logo--}}
    <a href="{{route('dashboard')}}" title="Apimio" class="list-group-item list-group-item-action border-0 bg-white">
        <div class="row">
            <div
                class="col-5 {{(Request::segment(1) == 'products')?'pl-2':((Request::segment(1) == 'channel')?'pl-2':((Request::segment(1) == 'product')?'pl-2': 'pl-5 pr-0'))}}">
                <img src="{{asset('media/sidebar/apimio-small.svg')}}" alt="">
            </div>
            <div class="col-7 pl-1 align-self-center">
                <h3 class="Poppins mt-2 bold font-18 {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                    {{ trans('sidebar.apimio') }}
                </h3>
            </div>
        </div>
    </a>
    <!--SPACE-->
    <div class="p-2 bg-white" style="z-index: 1;"></div>

    <a class="list-group-item list-group-item-action border-radius pt-2 icon-switch ml-00 {{(Request::segment(1) == 'products')?'ml-1 ': ((Request::segment(1) == 'channel')?'ml-1':((Request::segment(1) == 'product')?'ml-1':'ml-2 bg-grey'))}}"
       style="height: 58px;width: 220px;cursor: default">
        <div class="row">
            <div class="col-3 px-1">
                <span onclick="location = '{{route('organization.index')}}'" title="Switch Organization"
                      class="circle circle-warning text-center font-18 Roboto bold cursor-pointer"> {{strtoupper(Auth::user()->fname[0])}} </span>
            </div>
            @php
                $name= '';
                $name = \App\Models\Organization\Organization::where('id',auth()->user()->organization_id)->value('name');
            @endphp
            <div
                class="col-6 pl-2 pr-0 Roboto align-items-center {{(Request::segment(1) == 'products')?'d-none': ((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-flex')) }}">
                <div class="d-flex flex-column">
                    <div class="bold text-truncate" title="{{Auth::user()->fname}}" style="width: 103px;">
                        {{Auth::user()->fname}}
                    </div>
                    <div class="text-dark text-truncate" title="{{$name}}" style="width: 103px;"><i>{{$name}}</i></div>
                </div>
            </div>
            <div class="col-3 d-flex align-items-center ">
                @if(Auth::check() && count(Auth::user()->organizations) <= 1)
                    <span onclick="location = '{{route('organization.create')}}'" class="circle circle-light-filled p-0 cursor-pointer" title="Add Organization">
                        <i class="flaticon flaticon-add bold text-primary" style="font-size: 27px;"></i>
                    </span>
                @else
                    <span onclick="location = '{{route('organization.index')}}'" class="circle circle-light-filled cursor-pointer" title="Switch Organization">
                        <i class="flaticon flaticon-switch-org text-primary" style="font-size: 20px;"></i>
                    </span>
                @endif
            </div>
        </div>
    </a>

    <!--SPACE-->
    <div class="p-2 bg-white" style="z-index: 1;"></div>

    <a href="{{ route('dashboard') }}" title="Dashboard"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'dashboard')?'active':''}}">

        <div class="row">
            <div class="col-3 pr-1">
                <i class="flaticon flaticon-dashboard d-flex align-items-center icon-size"></i>
            </div>

            <span
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}"
                style="margin-top: 1px;">{{ trans('sidebar.dashboard') }} </span>
        </div>
    </a>
    <a href="{{route('products.index')}}" id="products" title="Products"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'products')?'active':((Request::segment(1) == 'channel')?'active':((Request::segment(1) == 'product')?'active':''))}}">
        <div class="row">
            <div class="col-3 pr-1">
                <i class="flaticon flaticon-product d-flex align-items-center icon-size"></i>
            </div>

            <div
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.products') }}
            </div>
        </div>
    </a>

    <a href="{{route('gallery.index')}}" id="products" title="Gallery"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'gallery') ? 'active' : ''}}">
        <div class="row">
            <div class="col-3 pr-1">
                <i class="fa-solid fa-images" style="font-size: 1.25rem"></i>
            </div>

            <div
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.gallery') }}
            </div>
        </div>
    </a>

    <div
        class="sidebar-sub-menu bg-light-grey-1 border-0 sidebar-width {{(Request::segment(1) == 'products')?'active':((Request::segment(1) == 'channel')?'active':((Request::segment(1) == 'product')?'active':'d-none'))}}"
        style="z-index: 20;left: 62px;right: 0;top: 0;position: absolute;height: 100%;
         border: 1px solid #E2E2E3;
             width: 172px!important;">

        <!--SPACE-->
        <div class="p-5 bg-light-grey-1 list-group-item list-group-item-action border-0 disabled"></div>
        <div class="bg-light-grey-1 list-group-item list-group-item-action border-0 disabled"
             style="padding: 1.8rem"></div>

        <a href="{{route('products.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(1) == 'products' && Request::segment(2) == null)?'active1':''}}">
            <span class="p-0 Roboto " style="">{{ trans('sidebar.all_products') }}</span>
        </a>
        <a href="{{route('family.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'family')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.attribute_sets') }}</span>
        </a>
        <a href="{{route('attributes.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'attributes')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.attributes') }}</span>
        </a>
        <a href="{{route('variant_attribute.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'variant-attribute')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.variant_options') }}</span>
        </a>
        <a href="{{route('brands.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'brands')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.brands') }}</span>
        </a>
        <a href="{{route('categories.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'categories')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.categories') }}</span>
        </a>
        <a href="{{route('versions.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'versions')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.languages') }}</span>
        </a>

        @can('add_and_edit_product' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])
            <a href="{{route('import.csv.step1')}}"
               class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'import')?'active1':''}}">
                <span class="col-9 p-0 Roboto ">{{ trans('sidebar.import') }}</span>
            </a>
        @endcan
        <a href="{{route('export.exportOne')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(2) == 'export')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.export') }}</span>
        </a>
        <a href="{{route('channel.index')}}"
           class="bg-light-grey-1 list-group-item list-group-item-action border-0 {{(Request::segment(1) == 'channel')?'active1':''}}">
            <span class="col-9 p-0 Roboto ">{{ trans('sidebar.stores') }}</span>
        </a>
    </div>


    <a href="{{ route('retailer.index') }}" title="Retailers"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'retailer')?'active':''}}">
        <div class="row">
            <div class="col-3 pr-1"><i class="flaticon flaticon-retailer bold d-flex align-items-center icon-size"></i>
            </div>

            <div
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.retailers') }}
            </div>
        </div>
    </a>

    <a href="{{ route('vendor.index') }}" title="Vendors"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'vendors')?'active':''}}">
        <div class="row">
            <div class="col-3 pr-1"><i class="flaticon flaticon-vendor d-flex align-items-center icon-size"></i></div>

            <div
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.vendors') }}
            </div>
        </div>
    </a>


    {{--<a href="{{ route('library.view') }}" title="Library"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(2) == 'view')?'active':''}}">
        <div class="row">
            <div class="col-3 pr-1">
                <i class="flaticon flaticon-gallery d-flex align-items-center icon-size"></i>
            </div>

            <div class="col-9 p-0 Roboto align-items-center {{(Request::segment(1) == 'products')?'d-none':'d-flex'}}">{{__('Media Library')}}</div>
        </div>
    </a>--}}
    @can('invite_team' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])

        <a href="{{ route('organization.invite_team.index') }}" title="Invite Team"
           class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'invite_team')?'active':''}}">
            <div class="row">
                <div class="col-3 pr-1">
                    <i class="flaticon flaticon-teams d-flex align-items-center icon-size"></i></div>

                <div
                    class="col-9 p-0 Roboto align-items-center {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                    {{ trans('sidebar.invite_team') }}
                </div>
            </div>
        </a>
    @endcan

    @can('perform_billing' , [\App\Models\Organization\OrganizationUserPermission::class , auth()->user()->organization_id])
        <a href="{{ route('billing') }}" title="Billing"
           class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'billing' || Request::segment(2) == 'billing')?'active':''}}">
            <div class="row">
                <div class="col-3 pr-1">
                    <i class="flaticon flaticon-plans d-flex align-items-center icon-size"></i></div>

                <div
                    class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                    {{ trans('sidebar.billing') }}
                </div>
            </div>
        </a>
    @endcan

    <a href="{{route("notification.index")}}" title="Notifications"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(1) == 'notification')?'active':''}}">
        <div class="row">
            <div class="col-3 pr-1">
                <i class="flaticon flaticon-notification d-flex align-items-center icon-size"></i>
                @if(count(auth()->user()->unreadNotifications ) > 0)
                    <span class="circle circle-danger-sm"
                          style="position: absolute;top: -1px;left: 29px;z-index: 1"></span>
                @endif
            </div>

            <div
                class="col-9 p-0 Roboto align-items-center {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.notification') }}
                {{--                <span--}}
                {{--                    class="badge badge-pill badge-light" style="height: 25px;padding:6px">--}}
                {{--                    {{count(auth()->user()->unreadNotifications )}}--}}
                {{--                </span>--}}
            </div>
        </div>
    </a>

    <a href="{{route('pages.settings')}}" title="Settings"
       class="list-group-item list-group-item-action icon-sidebar {{(Request::segment(2) == 'settings')?'active':''}}">
        <div class="row">
            <div class="col-3 pr-1">
                <i class="flaticon flaticon-settings d-flex align-items-center icon-size"></i>
            </div>

            <span
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.settings') }}
            </span>
        </div>
    </a>

    <a id="sidebar_supp_btn" href="https://support.apimio.com/" target="_blank" title="Help"
       class="list-group-item list-group-item-action icon-sidebar ">
        <div class="row">
            <div class="col-3 pr-1">
                <i class="flaticon flaticon-help d-flex align-items-center icon-size"></i>
            </div>

            <span
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.help') }}
            </span>
        </div>
    </a>

    <a id="sidebar_logout_btn" href="{{--{{ route('logout') }}--}}" title="Logout"
       class="list-group-item list-group-item-action icon-sidebar "
       onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
       aria-label="Logout" data-microtip-position="right" role="tooltip">
        <div class="row">
            <div class="col-3 pr-1">
                <i class="flaticon flaticon-logout d-flex align-items-center icon-size"></i>
            </div>

            <span
                class="col-9 p-0 Roboto {{(Request::segment(1) == 'products')?'d-none':((Request::segment(1) == 'channel')?'d-none':((Request::segment(1) == 'product')?'d-none':'d-block'))}}">
                {{ trans('sidebar.logout') }}
            </span>
        </div>
        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
            @csrf
        </form>
    </a>

    @php($org = \App\Models\Organization\Organization::where('id',\Illuminate\Support\Facades\Auth::user()->organization_id)->first())
    @if($org->onTrial())
        {{--  This code is use for billing time  --}}
        <div class="mt-3">
            <div class="position-relative pt-2 d-flex align-items-end time-alert">
                <div class="billing-custom-css mx-auto" style="z-index: 1000;position: relative">
                    <div>
                    </div>
                    <div class="mb-3">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                            <path d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z" stroke="#DC3545" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M15.7089 15.1798L12.6089 13.3298C12.0689 13.0098 11.6289 12.2398 11.6289 11.6098V7.50977" stroke="#DC3545" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>

                    <h5><b>Trial Plan</b></h5>
                    <p class="mb-3">{{now()->diffInDays(\Carbon\Carbon::parse($org->trial_ends_at))}} Days left in your trial.  </p>
                    <a href="{{route('billing')}}" class=" upgrade-btn-css" >Click to Upgrade </a>
                </div>
            </div>
        </div>

    @endif
</div>

@push('footer_scripts')
    <script>

        let page1;
        let page2;

        $(document).ready(function () {
            let someVarName = localStorage.getItem("page1");

            if (someVarName !== "") {
                $(".sidebar-sub-menu").show();
            }
        });
        $('.icon-menu').click(function () {
            document.getElementById("sidebar-width").style.transition = "0.5s";
            document.getElementById("sidebar-width").style.width = "236px";
            // $("#sidebar-width").toggleClass("main");
        });

        $('#close').click(function () {
            document.getElementById("sidebar-width").style.width = "0";
        });


        $('.sidebar-btn').click(function () {
            document.getElementById("sidebar-width").style.transition = "0.5s";
            document.getElementById("sidebar-width").style.width = "236px";
        });


    </script>
@endpush
