<style>
    .disabled-link {
        opacity: 0.5;
    }
</style>
<div class="col-auto col-md-3 col-xl-2 px-0 position-relative border main-sidebar px-2">
    <div class="d-flex flex-column align-items-center align-items-sm-start text-white min-vh-100">
        <a href="{{ route('dashboard') }}"
            class="d-flex align-items-center py-4 mb-md-0 me-md-auto text-white text-decoration-none justify-content-center mx-auto">
            <img id="logo_sidebar" src="{{ asset('assets/images/apimio-small.svg') }}" class="d-block d-sm-inline"
                alt="logo" /><span class="ms-2 d-none d-sm-inline text-black fw-700 h2 mb-0">Apimio</span>
        </a>
        @php
            $name = '';
            $name = \App\Models\Organization\Organization::where('id', auth()->user()->organization_id)->value('name');
        @endphp
        <div
            class="d-flex align-items-center bg-light mb-md-0 me-md-auto text-white justify-content-around text-decoration-none w-100 user-information py-1">
            <span class="circle bg-warning">{{ strtoupper(Auth::user()->fname[0]) }}</span>
            <span class="ms-2 d-none d-sm-inline text-black">
                <h4 class="fw-700 mb-0 d-none d-lg-block" title="{{ Auth::user()->fname }}">
                    <?php
                    $fname = Auth::user()->fname;
                    if (strlen($fname) > 15) {
                        $fname = substr($fname, 0, 15) . '...';
                    }
                    echo $fname;
                    ?>
                </h4>
                <p class="mb-0 d-none d-lg-block fs-14" title="{{ $name }}">
                    <?php
                    $name = $name ?? ''; // Ensure $name is defined
                    echo strlen($name) > 15 ? substr($name, 0, 15) . '...' : $name;
                    ?>
                </p>
            </span>

            @if (Auth::check() && count(Auth::user()->organizations) <= 1)
                <a href="{{ route('organization.create') }}" class="add-organization d-none d-lg-block">
                    <span class="bg-white p-2 circle shadow">
                        <img src="{{ asset('./assets/images/plus.svg') }}" alt="event" />
                    </span>
                </a>
            @else
                <a href="{{ route('organization.index') }}" class="d-none d-lg-block">
                    <span class="bg-white p-2 circle shadow">
                        <img src="{{ asset('./assets/images/export.svg') }}" alt="event" />
                    </span>
                </a>
            @endif


        </div>

        <ul class="nav nav-pills flex-column mb-sm-auto mb-0 w-100 pt-4" id="menu">
            <li class="nav-item pl-12 dashboard-link {{ Request::segment(1) == 'dashboard' ? 'active-link' : '' }}">
                <a href="{{ route('dashboard') }}"
                    class="nav-link align-middle px-0  {{ Request::segment(1) == 'dashboard' ? 'active-link-product' : '' }}">
                    <span class="icon icon-category" title="Dashboard"></span>
                    <span class="ms-3 d-none d-sm-inline">Dashboard</span>
                </a>
            </li>
            @can('SubscriptionAccess', 'product')
                <li class="nav-item pl-12 {{ Request::segment(1) == 'products' ? 'active-link' : '' }}">
                    <a href="{{ route('products.index') }}"
                        class="nav-link align-middle px-0 {{ Request::segment(1) == 'products' ? 'active-link-product' : (Request::segment(1) == 'channel' ? 'active-link-product' : (Request::segment(1) == 'product' ? 'active-link-product' : '')) }}">
                        <span class="icon icon-product" title="Product"></span>
                        <span class="ms-3 d-none d-sm-inline">Product</span>
                    </a>
                </li>
            @else
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="icon icon-product" title="Product"></span>
                            <span class="ms-3 d-none d-sm-inline">Product</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            @endcan
            @can('SubscriptionAccess', 'gallery')
                <li class="nav-item pl-12 {{ Request::segment(1) == 'gallery' ? 'active-link' : '' }}">
                    <a href="{{ route('gallery.index') }}"
                        class="nav-link align-middle min-w-full px-0 {{ Request::segment(1) == 'gallery' ? 'active-link-product' : '' }}">
                        <i class="icon fa-regular fa-images" title="Gallery"></i>
                        <span class="ms-3 d-none d-sm-inline">DAM</span>
                    </a>
                </li>
            @else
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <i class="icon fa-regular fa-images" title="Gallery"></i>
                            <span class="ms-3 d-none d-sm-inline">DAM</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            @endcan
            @can('SubscriptionAccess', 'gallery')
                <li class="nav-item pl-12 {{ Request::segment(1) == 'brandportal' ? 'active-link' : '' }}">
                    <a href="/brandportal/create"
                        class="nav-link align-middle min-w-full px-0 {{ Request::segment(1) == 'brandportal' ? 'active-link-product' : '' }}">
                        <i class="icon fa-regular fa-solid fa-globe"></i>

                        <span class="ms-3 d-none d-sm-inline">Brand Portal</span>
                    </a>
                </li>
            @else
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-start w-100">
                            <i class="icon fa-regular fa-solid fa-globe"></i>


                            <span class="ms-3 d-none d-sm-inline">Brand Portal</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>


                        </div>
                    </a>
                </li>
            @endcan
            {{--            TODO: we need to do in future not to delete this code.(Iqtidar) --}}
            {{--            <li class="nav-item pl-12 {{(Request::segment(1) == 'retailer')?'active-link':''}}"> --}}
            {{--                <a href="{{ route('retailer.index') }}" class="nav-link align-middle px-0 {{(Request::segment(1) == 'retailer')?'active-link-product':''}}"> --}}
            {{--                    <span class="icon icon-retailer" title="Retailer"><span class="path1"></span><span class="path2"></span></span> --}}
            {{--                    <span class="ms-3 d-none d-sm-inline">Retailer</span> --}}
            {{--                </a> --}}
            {{--            </li> --}}
            {{--            <li class="nav-item pl-12 {{(Request::segment(1) == 'vendors')?'active-link':''}}"> --}}
            {{--                <a href="{{ route('vendor.index') }}" class="nav-link align-middle px-0 {{(Request::segment(1) == 'vendors')?'active-link-product':''}}"> --}}
            {{--                    <span class="icon icon-vendors" title="Vendors"></span> --}}
            {{--                    <span class="ms-3 d-none d-sm-inline">Vendors</span> --}}
            {{--                </a> --}}
            {{--            </li> --}}
            @can('invite_team', [\App\Models\Organization\OrganizationUserPermission::class,
                auth()->user()->organization_id])
                <li class="nav-item pl-12 {{ Request::segment(1) == 'invite_team' ? 'active-link' : '' }}">
                    <a href="{{ route('organization.invite_team.index') }}"
                        class="nav-link align-middle px-0 {{ Request::segment(1) == 'invite_team' ? 'active-link-product' : '' }}">
                        <span class="icon icon-teams" title="Invite Team"></span>
                        <span class="ms-3 d-none d-sm-inline">Invite Team</span>
                    </a>
                </li>
            @endcan
            @can('perform_billing', [\App\Models\Organization\OrganizationUserPermission::class,
                auth()->user()->organization_id])
                <li
                    class="nav-item pl-12 {{ Request::segment(1) == 'billing' || Request::segment(2) == 'billing' ? 'active-link' : '' }}">
                    <a href="{{ route('billing') }}"
                        class="nav-link align-middle px-0 {{ Request::segment(1) == 'billing' || Request::segment(2) == 'billing' ? 'active-link-product' : '' }}">
                        <span class="icon icon-billing" title="Plans"></span>
                        <span class="ms-3 d-none d-sm-inline">Plans</span>
                    </a>
                </li>
            @endcan
            @can('SubscriptionAccess', 'notification')
                <li
                    class="nav-item pl-12 {{ Request::segment(1) == 'notification' ? 'active-link' : '' }} position-relative">
                    <a href="{{ route('notification.index') }}"
                        class="nav-link align-middle px-0 {{ Request::segment(1) == 'notification' ? 'active-link-product' : '' }}">
                        <span class="icon icon-bell-regular" title="Notifications"></span>
                        <span class="ms-3 d-none d-sm-inline">Notifications</span>
                    </a>
                    @auth()
                        @if (auth()->user()->unreadNotifications->where('organization_id', auth()->user()->organization_id)->count() > 0)
                            <span class="notification notification-danger me-2 position-absolute sidebar-notification"></span>
                        @endif
                    @endauth
                </li>
            @else
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="icon icon-bell-regular" title="Notifications"></span>
                            <span class="ms-3 d-none d-sm-inline">Notifications</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            @endcan
            <li class="nav-item pl-12">
                <a href="https://support.apimio.com/" target="_blank" title="Help"
                    class="nav-link align-middle px-0">
                    <span class="icon icon-help" title="Help"></span>
                    <span class="ms-3 d-none d-sm-inline">Help</span>
                </a>
            </li>
            @can('SubscriptionAccess', 'settings')
                <li class="nav-item pl-12 w-100 {{ Request::segment(2) == 'settings' ? 'active-link' : '' }}">
                    <a href="{{ route('pages.settings') }}"
                        class="nav-link align-middle px-0 text-decoration-none {{ Request::segment(2) == 'settings' ? 'active-link-product' : '' }}">
                        <span class="icon icon-setting" title="SETTINGS"></span>
                        <span class="ms-3 d-none d-sm-inline">Settings</span>
                    </a>
                </li>
            @else
                <li class="nav-item pl-12 disabled-link">
                    <a href="javascript:void(0)" class="nav-link align-middle px-0" data-bs-toggle="tooltip"
                        data-bs-placement="top"
                        title="This feature is available only in the Advanced PIM package or higher.">
                        <div class="d-flex align-items-center justify-content-between w-100">
                            <span class="icon icon-setting" title="SETTINGS"></span>
                            <span class="ms-3 d-none d-sm-inline">Settings</span>
                            <i class="fa fa-lock text-secondary ms-auto pe-2"></i>
                        </div>
                    </a>
                </li>
            @endcan
            <li class="nav-item pl-12 w-100">
                <a href="/login.html" class="nav-link align-middle px-0 text-decoration-none"
                    onclick="event.preventDefault(); document.getElementById('logout-form').submit();"
                    aria-label="Logout" data-microtip-position="right" role="tooltip">
                    <span class="icon icon-logout" title="LOG OUT"></span>
                    <span class="ms-3 d-none d-sm-inline">Log Out</span>
                </a>
                <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                    @csrf
                </form>
            </li>

            @php($org = \App\Models\Organization\Organization::where('id', \Illuminate\Support\Facades\Auth::user()->organization_id)->first())
            @if ($org && $org->onTrial())
                {{--  if user is on trial period  --}}
                <div class="mt-5 billing-timer">
                    <div class="position-relative">
                        <div class="billing-custom-css billing-custom-css_main  billing-custom-css-main-menu mx-auto d-none d-lg-block"
                            style="z-index: 1000;position: absolute">
                            <div>
                            </div>
                            <div class="mb-3 text-center">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                                    <path
                                        d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
                                        stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M15.7089 15.1798L12.6089 13.3298C12.0689 13.0098 11.6289 12.2398 11.6289 11.6098V7.50977"
                                        stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </div>

                            <h3 class="text-center"><b>Trial Plan</b></h3>
                            <p class="mb-3 text-center">
                                {{ now()->diffInDays(\Carbon\Carbon::parse($org->trial_ends_at)) }} Days left in your
                                trial.</p>
                            <div class="text-center"> <a
                                    href="{{ route('billing') }}"class="btn btn-sm text-white bg-danger">Click to
                                    Upgrade </a></div>
                        </div>
                    </div>
                </div>
            @elseif($org && $org->is_subscribed())
                {{-- When user subscribed --}}
                @if ($org->subscription('default')?->canceled())
                    {{--  when user susbcription gets   --}}
                    <div class="mt-5 billing-timer">
                        <div class="position-relative">
                            <div class="billing-custom-css billing-custom-css_main  billing-custom-css-main-menu mx-auto d-none d-lg-block"
                                style="z-index: 1000;position: absolute">
                                <div>
                                </div>
                                <div class="mb-3 text-center">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
                                            stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path
                                            d="M15.7089 15.1798L12.6089 13.3298C12.0689 13.0098 11.6289 12.2398 11.6289 11.6098V7.50977"
                                            stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>
                                </div>

                                <h3 class="text-center"><b>Subscription canceled</b></h3>
                                <p class="mb-3 text-center">
                                    {{ now()->diffInDays(\Carbon\Carbon::parse($org->remaining_grace_period())) }} Days
                                    left in your plan.</p>
                                <div class="text-center"> <a
                                        href="{{ route('billing') }}"class="btn btn-sm text-white bg-danger">Click to
                                        Resume </a></div>
                            </div>
                        </div>
                    </div>
                @endif
            @else
                {{-- when user trial ends and he did not subscribed --}}
                <div class="mt-5 billing-timer">
                    <div class="position-relative">
                        <div class="billing-custom-css billing-custom-css_main  billing-custom-css-main-menu mx-auto d-none d-lg-block"
                            style="z-index: 1000;position: absolute">
                            <div>
                            </div>
                            <div class="mb-3 text-center">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg" class="mx-auto">
                                    <path
                                        d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z"
                                        stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                    <path
                                        d="M15.7089 15.1798L12.6089 13.3298C12.0689 13.0098 11.6289 12.2398 11.6289 11.6098V7.50977"
                                        stroke="#DC3545" stroke-width="1.5" stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </div>

                            <h3 class="text-center"><b>Trial Plan</b></h3>
                            <p class="mb-3 text-center">Trial expired.</p>
                            <div class="text-center"> <a
                                    href="{{ route('billing') }}"class="btn btn-sm text-white bg-danger">Click to
                                    Upgrade </a></div>
                        </div>
                    </div>
                </div>
            @endif

        </ul>
    </div>
</div>
@push('footer_scripts')
    <script>
        $(document).ready(function() {
            const dashboard_link = document.querySelector('.dashboard-link');
            const main_sidebar = document.querySelector('.main-sidebar');
            const planTime = document.querySelector('.billing-custom-css');
            window.addEventListener("resize", function() {
                const screen_width = document.documentElement.clientWidth;
                if (screen_width <= 768) {
                    $('.add-organization').addClass('d-none')
                }
            });
            dashboard_link.addEventListener('click', function(e) {
                //  e.preventDefault();
                main_sidebar.classList.toggle("mobile-menu");
                planTime.classList.toggle('d-none');
                if ($('.add-organization').hasClass('d-none')) {
                    $('.add-organization').removeClass('d-none');
                } else {
                    $('.add-organization').addClass('d-none')
                }

            })

            $('.right-side').on('click', function() {
                if (main_sidebar.classList.contains("mobile-menu")) {
                    main_sidebar.classList.remove('mobile-menu');
                }
            });

            var currentUrl = '{{ route('dashboard') }}';
            console.log(currentUrl, window.location.href)
            dashboard_link.addEventListener('click', function(e) {
                if (window.location.href === currentUrl) {
                    e.preventDefault();
                }
            });

        });
    </script>
    <script>
        // Initialize Bootstrap tooltips with custom trigger option
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
            var tooltip = new bootstrap.Tooltip(tooltipTriggerEl, {
                trigger: 'hover focus'
            });

            // Hide tooltip on click
            tooltipTriggerEl.addEventListener('click', function() {
                tooltip.hide();
            });

            return tooltip;
        });
    </script>
@endpush
