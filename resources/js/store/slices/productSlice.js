import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { post } from '../../axios';

export const fetchProducts = createAsyncThunk(
    'products/fetchProducts',
    async ({ page = 1, pageSize = 10, filters = null }, { rejectWithValue }) => {
        try {
            const requestPayload = filters ? { filters } : {};
            const response = await post(`products/index?paginate=${pageSize}&page=${page}`, requestPayload);
            return response;
        } catch (error) {
            return rejectWithValue(error.message);
        }
    }
);

const initialState = {
    products: [],
    filteredData: null,
    pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
    },
    activeFilters: null,
    loading: false,
    error: null,
};

export const productSlice = createSlice({
    name: 'products',
    initialState,
    reducers: {
        setProducts: (state, action) => {
            state.products = action.payload;
        },
        setFilteredData: (state, action) => {
            state.filteredData = action.payload;
        },
        setPagination: (state, action) => {
            state.pagination = {
                ...state.pagination,
                ...action.payload,
            };
        },
        setActiveFilters: (state, action) => {
            state.activeFilters = action.payload;
        },
        clearFilters: (state) => {
            state.activeFilters = null;
            state.filteredData = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchProducts.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchProducts.fulfilled, (state, action) => {
                state.loading = false;
                state.products = action.payload.data;
                if (action.payload.pagination) {
                    state.pagination = {
                        current: action.payload.pagination.current_page,
                        pageSize: action.payload.pagination.per_page,
                        total: action.payload.pagination.total,
                    };
                }
            })
            .addCase(fetchProducts.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            });
    },
});

// Export actions
export const {
    setProducts,
    setFilteredData,
    setPagination,
    setActiveFilters,
    clearFilters,
} = productSlice.actions;

// Selectors
export const selectProducts = (state) => state.products.products;
export const selectFilteredData = (state) => state.products.filteredData;
export const selectPagination = (state) => state.products.pagination;
export const selectActiveFilters = (state) => state.products.activeFilters;
export const selectLoading = (state) => state.products.loading;

export default productSlice.reducer;
