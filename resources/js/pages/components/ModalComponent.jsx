import React, { useState, useMemo, useEffect } from "react";
import { Modal, Button, Select, Form, Switch, Input, message } from "antd";
import { router } from '@inertiajs/react';
// import { Inertia } from "@inertiajs/inertia"; // ⬅️  Inertia helper
// import route from "ziggy-js";
import axios from "axios";

const ModalComponent = ({ isVisible, onCancel, dataRequired, nodes, isSaveTemplate = false }) => {
  const [form] = Form.useForm();
  // Initialize saveTemplate state based on the isSaveTemplate prop
  const [saveTemplate, setSaveTemplate] = useState(isSaveTemplate);

  /* ---------- helpers ---------- */
  const objectToOptions = (obj) =>
    Object.entries(obj).map(([value, label]) => ({ value, label }));

  /* ---------- dynamic select options ---------- */
  const storeOptions = useMemo(
    () => objectToOptions(dataRequired.catalogs),
    [dataRequired.catalogs]
  );
  const languageOptions = useMemo(
    () => objectToOptions(dataRequired.versions),
    [dataRequired.versions]
  );

  /* ---------- form defaults ---------- */
  useEffect(() => {
    // Only populate the form when the modal is visible
    if (!isVisible) return;

    if (dataRequired?.selected_template) {
      isSaveTemplate = true;
      setSaveTemplate(true);
    }
    else {
      isSaveTemplate = false;
      setSaveTemplate(false);
      form.setFieldValue("templateName", "")
    }

    // Default values
    const defaultValues = {
      status: "1", // "1" = published | "0" = draft
      saveTemplate: isSaveTemplate, // Set based on prop
    };

    // Auto-select language if there's only one option
    if (languageOptions.length === 1) {
      defaultValues.language = languageOptions[0].value;
      console.log(`Auto-selected language: ${languageOptions[0].label} (${languageOptions[0].value})`);
    }

    // Auto-select stores if there's only one option
    if (storeOptions.length === 1) {
      defaultValues.stores = [storeOptions[0].value];
      console.log(`Auto-selected store: ${storeOptions[0].label} (${storeOptions[0].value})`);
    }

    // Check if we have a selected template in dataRequired
    if (dataRequired?.selected_template) {
      const template = dataRequired.selected_template;

      // Populate form with template data (template values override auto-selection)
      defaultValues.language = template.version;
      defaultValues.status = template.product_status?.toString() || "1";

      // Handle catalog/stores - ensure it's an array of strings
      if (template.catalog) {
        if (Array.isArray(template.catalog)) {
          defaultValues.stores = template.catalog.map(item => item.toString());
        } else {
          defaultValues.stores = [template.catalog.toString()];
        }
      }

      // Set template name if available
      if (template.temp_name) {
        defaultValues.templateName = template.temp_name;
      }

      // Set template ID in hidden field if available
      if (template.temp_id) {
        defaultValues.templateId = template.temp_id.toString();
      }
    }

    // Set form values
    form.setFieldsValue(defaultValues);

    // If it's save template mode, auto-set the template name field
    if (isSaveTemplate) {
      // Make sure the template name field is set and visible
      form.setFieldValue('templateName', form.getFieldValue('templateName') || '');
    }
  }, [form, isSaveTemplate, dataRequired, isVisible, languageOptions, storeOptions]);

  /* ---------- submit handlers ---------- */
  // Common function to validate form fields
  const validateForm = async () => {
    try {
      return await form.validateFields();
    } catch (error) {
      console.error("Form validation error:", error);
      return null;
    }
  };

  // Create a common payload builder function
  const createPayload = (values, isTemplateSave = false) => {
    return {
      organization_id: dataRequired.organization_id,
      template_method_type: dataRequired.template_method_type, // e.g. "import"
      nodes: {
        data: nodes, // what you passed in
      },
      version: values.language, // language id / code
      catalog: values.stores, // one or many store IDs
      status: values.status ?? "0", // "1" or "0"

      // Fields specifically for template saving
      temp_status: isTemplateSave ? "on" : "off",
      temp_name: isTemplateSave ? (values.templateName || "") : "Apimio Default",
      temp_id: values.templateId || "", // Include template ID if available for updates

      // Additional fields from the previous payload format
      export_type: "",
      import_action: dataRequired.import_action || 3,
      file_path: dataRequired.file_path || "",
      ignore_unmapped: "off"
    };
  };

  // Handler for saving template
  const handleSaveTemplate = async () => {
    // Make sure templateName is required when saving a template
    if (!form.getFieldValue('templateName')) {
      form.validateFields(['templateName']);
      return;
    }

    const values = await validateForm();
    if (!values) return;

    const payload = createPayload(values, true);
    const endpoint = "/products/import/save-template";



    try {
      const response = await axios.post(endpoint, payload);

      router.visit('/products/import/apply-template', {
        method: 'get',
        data: {
          template_id: response?.data?.data?.selected_template?.temp_id
        },
        replace: true,
        preserveState: true,
        preserveScroll: true,
        only: ['data']
      });



      // Update dataRequired with the selected_template from the response
      if (response.data.data.selected_template) {
        if (dataRequired.selected_template) {
          // Update existing selected_template
          dataRequired.selected_template = response.data.data.selected_template;
        } else {
          // Add selected_template to dataRequired if it doesn't exist
          dataRequired.selected_template = response.data.data.selected_template;
        }

      }

      if (response.data.status === "success") {
        message.success("Template saved successfully");
        onCancel();
      } else {
        console.log("Failed to save template:", response.data.message);
        message.error(response.data.message || "Failed to save template");
      }
    } catch (error) {
      console.error("Error saving template:", error);
      if (error.response) {
        console.error("Response data:", error.response.data);
        message.error(error.response.data.message || "Failed to save template");
      } else {
        message.error("Failed to save template: " + error.message);
      }
    }
  };

  // Handler for starting import
  const handleStartImport = async () => {
    const values = await validateForm();
    if (!values) return;

    // For import, we need to check if the user wants to save the template as well
    const isTemplateSave = saveTemplate;
    const payload = createPayload(values, isTemplateSave);

    // If saving template is enabled, make sure we have a template name
    if (isTemplateSave && !values.templateName) {
      form.validateFields(['templateName']);
      return;
    }

    // const endpoint = dataRequired.redirect_url_route ?? "/products/import";
      const endpoint = "/products/import";

    console.log(endpoint,"endpoint");

    try {
      // const response = await axios.post(endpoint, payload);

        router.post(endpoint, payload, {
            onSuccess: () => {
                message.success("Import started successfully");
                window.location.href = "/products";
            },
            onError: () => {
                console.log("Failed to start import:", response.data.message);
                message.error(response.data.message || "Failed to start import");
            },
        });


      // if (response.data.status === "success") {
      //   message.success("Import started successfully");
      //   window.location.href = "/products";
      // } else {
      //   console.log("Failed to start import:", response.data.message);
      //   message.error(response.data.message || "Failed to start import");
      // }
    } catch (error) {
      console.error("Error starting import:", error);
      if (error.response) {
        console.error("Response data:", error.response.data);
        message.error(error.response.data.message || "Failed to start import");
      } else {
        message.error("Failed to start import: " + error.message);
      }
    }
  };

  // Note: We've replaced the generic handleSubmit with specific handlers for each action

  const handleSwitchChange = (checked) => {
    setSaveTemplate(checked);
    if (!checked && !dataRequired.selected_template) form.setFieldValue("templateName", "");
  };



  return (
    <Modal
      title={isSaveTemplate ? "Save Template" : "Import Configuration"}
      open={isVisible}
      centered
      onCancel={onCancel}
      footer={null}
      width={500}
    >
      <Form form={form} layout="vertical" className="mt-4">
        {/* HIDDEN TEMPLATE ID FIELD */}
        <Form.Item
          name="templateId"
          hidden={true}
        >
          <Input type="hidden" />
        </Form.Item>

        {/* LANGUAGE */}
        <Form.Item
          label={<span className="font-medium">Select Language</span>}
          name="language"
          rules={[{ required: true, message: "Please select a language" }]}
        >
          <Select
            showSearch={languageOptions.length > 1}
            placeholder={
              languageOptions.length === 1
                ? `Auto-selected: ${languageOptions[0].label}`
                : "Search language"
            }
            options={languageOptions}
            optionFilterProp="label"
          />
        </Form.Item>

        {/* STATUS */}
        <Form.Item
          label={<span className="font-medium">Select Status</span>}
          name="status"
          rules={[{ required: true, message: "Please select a status" }]}
        >
          <Select
            placeholder="Select status"
            options={[
              { value: "1", label: "Published" },
              { value: "0", label: "Draft" },
            ]}
          />
        </Form.Item>

        {/* STORES */}
        <Form.Item
          label={<span className="font-medium">Select Stores</span>}
          name="stores"
          rules={[
            { required: true, message: "Please select at least one store" },
          ]}
        >
          <Select
            mode="multiple"
            placeholder={
              storeOptions.length === 1
                ? `Auto-selected: ${storeOptions[0].label}`
                : "Select stores"
            }
            options={storeOptions}
            showSearch={storeOptions.length > 1}
          />
        </Form.Item>

        {/* SAVE-TEMPLATE SWITCH - Only show if not in save template mode */}
        {!isSaveTemplate && (
          <div className="flex items-center gap-4 mb-4">
            <span className="font-medium">
              Do you want to save template for later?
            </span>
            <div className="flex items-center gap-2">
              <span className="text-gray-600">No</span>
              <Form.Item
                name="saveTemplate"
                valuePropName="checked"
                className="mb-0"
              >
                <Switch onChange={handleSwitchChange} />
              </Form.Item>
              <span className="text-gray-600">Yes</span>
            </div>
          </div>
        )}

        {/* TEMPLATE NAME (conditional) - Always show in save template mode */}
        {(saveTemplate || isSaveTemplate) && (
          <Form.Item
            label={<span className="font-medium">Template Name</span>}
            name="templateName"
            initialValue=""
            rules={[
              { required: true, message: "Please enter a template name" },
            ]}
          >
            <Input
              placeholder="Enter template name"
              autoFocus={isSaveTemplate}
            />
          </Form.Item>
        )}

        {/* ACTION BUTTONS */}
        <div className="flex justify-end gap-2 mt-6">
          {isSaveTemplate ? (
            <>
              <Button onClick={onCancel}>Cancel</Button>
              <Button
                type="primary"
                onClick={handleSaveTemplate}
                className="bg-[#740898] text-white border border-[#740898] rounded-[4px]"
              >
                Save Template
              </Button>
            </>
          ) : saveTemplate ? (
            <>
              <Button
                type="primary"
                onClick={handleSaveTemplate}
                className="h-[32px] bg-white text-black border border-[#D9D9D9] rounded-[4px]"
              >
                Save Template
              </Button>
              <Button
                type="primary"
                onClick={handleStartImport}
                className="bg-[#740898] text-white border border-[#740898] rounded-[4px]"
              >
                Start Importing
              </Button>
            </>
          ) : (
            <>
              <Button onClick={onCancel}>Cancel</Button>
              <Button
                type="primary"
                onClick={handleStartImport}
                className="bg-[#740898] text-white border border-[#740898] rounded-[4px] hover:bg-white hover:text-[#740898]"
              >
                Start Importing
              </Button>
            </>
          )}
        </div>
      </Form>
    </Modal>
  );
};

export default ModalComponent;
