import React from "react";
import Sidebar from "./Sidebar";
import <PERSON>bar<PERSON><PERSON><PERSON><PERSON> from "./SidebarWithLogo";
import AppHeader from "./Header";
import { Flex, Layout } from "antd";
import { Head } from "@inertiajs/react";

const { Header, Footer, Sider, Content } = Layout;

const AppLayout = ({
                       children,
                       showHeader = true,
                       showSidebar = true,
                       headerStyle: customHeaderStyle = {},
                       contentStyle: customContentStyle = {},
                       siderStyle: customSiderStyle = {},
                       layoutStyle: customLayoutStyle = {},
                       activeMenuItem = "dashboard",
                       title,
                   }) => {
    const defaultHeaderStyle = {
        textAlign: "center",
        height: 80,
        lineHeight: "64px",
        width: "100%",
        padding: 0,
        position: "fixed",
        zIndex: 1,
    };

    const defaultContentStyle = {
        // set min height to 100vh-80px-80px
        // minHeight: `calc(100vh - 120px)`,
        // color: "#fff",
        backgroundColor: "#F9FAFB",
        marginLeft: showSidebar ? 200 : 0,
        marginTop: showHeader ? 80 : 0,
    };

    const defaultSiderStyle = {
        // overflow: "auto",
        height: "auto",
        position: "fixed",
        insetInlineStart: 0,
        top: showHeader ? 80 : 0,
        bottom: 0,
        scrollbarWidth: "thin",
    };

    const defaultLayoutStyle = {
        borderRadius: 0,
        overflow: "hidden",
        width: "100%",
        maxWidth: "100%",
    };

    // Merge default styles with custom styles
    const headerStyleMerged = { ...defaultHeaderStyle, ...customHeaderStyle };
    const contentStyleMerged = { ...defaultContentStyle, ...customContentStyle };
    const siderStyleMerged = { ...defaultSiderStyle, ...customSiderStyle };
    const layoutStyleMerged = { ...defaultLayoutStyle, ...customLayoutStyle };

    return (
        <Flex wrap>
            {title && <Head title={title} />}
            <Layout style={layoutStyleMerged} hasSider={showSidebar}>
                {showHeader && (
                    <Header style={headerStyleMerged}>
                        <AppHeader />
                    </Header>
                )}
                <Layout>
                    {showSidebar && (
                        <Sider width="200px" style={siderStyleMerged}>
                            {showHeader ? (
                                <Sidebar activeKey={activeMenuItem} />
                            ) : (
                                <SidebarWithLogo activeKey={activeMenuItem} />
                            )}
                        </Sider>
                    )}
                    <Content style={contentStyleMerged}>{children}</Content>
                </Layout>
            </Layout>
        </Flex>
    );
};

export default AppLayout;
