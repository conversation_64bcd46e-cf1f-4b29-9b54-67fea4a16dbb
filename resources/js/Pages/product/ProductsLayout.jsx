import React, { useRef, useState, useEffect } from "react";
import AppLayout from "../layout/AppLayout";
import { Link, usePage } from "@inertiajs/react";

// Tab menu items configuration
const tabItems = [
    {
        key: "all-products",
        label: "All Products",
        path: "/products",
    },
    {
        key: "importcsv",
        label: "Import",
        path: "/products/import/step1",
    },
    {
        key: "export",
        label: "Export",
        path: "/products/export/step1",
    },
    {
        key: "categories",
        label: "Categories",
        path: "/products/categories",
    },
    {
        key: "attribute-sets",
        label: "Attribute Sets",
        path: "/products/family",
    },
    {
        key: "attribute",
        label: "Attribute",
        path: "/products/attributes",
    },
    {
        key: "variant-options",
        label: "Variant Options",
        path: "/products/variant-attribute",
    },
    {
        key: "brands",
        label: "Brands",
        path: "/products/brands",
    },
    {
        key: "vendors",
        label: "Vendors",
        path: "/products/vendors",
    },
    {
        key: "languages",
        label: "Languages",
        path: "/products/versions",
    },
    {
        key: "locations",
        label: "Locations",
        path: "/products/locations",
    },
    {
        key: "stores",
        label: "Stores",
        path: "/channel",
    },
];

const ProductsLayout = ({ children, title, activeTab, hideHeader = false }) => {
    const { props } = usePage();
    const currentTab = activeTab ?? (props.tab || "all-products");
    const scrollContainerRef = useRef(null);
    const [isScrollable, setIsScrollable] = useState(false);

    useEffect(() => {
        const checkScrollable = () => {
            if (scrollContainerRef.current) {
                const { scrollWidth, clientWidth } = scrollContainerRef.current;
                setIsScrollable(scrollWidth > clientWidth);
            }
        };

        const timeoutId = setTimeout(checkScrollable, 100);
        window.addEventListener("resize", checkScrollable);

        return () => {
            clearTimeout(timeoutId);
            window.removeEventListener("resize", checkScrollable);
        };
    }, []);

    console.log(props,currentTab);
    return (
        <>
            {/* Custom scrollbar styles */}
            <style jsx>{`
                .nav-pills-scroll {
                    scrollbar-width: thin;
                    scrollbar-color: #d1d5db #f9fafb;
                }

                .nav-pills-scroll::-webkit-scrollbar {
                    height: 6px;
                }

                .nav-pills-scroll::-webkit-scrollbar-track {
                    background: #f9fafb;
                    border-radius: 3px;
                }

                .nav-pills-scroll::-webkit-scrollbar-thumb {
                    background: #d1d5db;
                    border-radius: 3px;
                }

                .nav-pills-scroll::-webkit-scrollbar-thumb:hover {
                    background: #9ca3af;
                }
            `}</style>

            <AppLayout
                activeMenuItem="products"
                title={title}
                showHeader={!hideHeader}>

            {/* Professional Nav Pills - Only show when hideHeader is false */}
            {!hideHeader && (
                <div className="bg-white border-b border-gray-200">
                    {/* Mobile: Dropdown Style */}
                    <div className="block sm:hidden px-4 py-3">
                        <div className="relative">
                            <select
                                value={currentTab}
                                onChange={(e) => {
                                    const selectedItem = tabItems.find(item => item.key === e.target.value);
                                    if (selectedItem) {
                                        window.location.href = selectedItem.path;
                                    }
                                }}
                                className="block w-full px-3 py-2 text-sm font-medium bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#740898] focus:border-[#740898] appearance-none"
                            >
                                {tabItems.map((item) => (
                                    <option key={item.key} value={item.key}>
                                        {item.label}
                                    </option>
                                ))}
                            </select>
                            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Tablet & Desktop: Nav Pills with Visible Scrollbar */}
                    <div className="hidden sm:block px-4 sm:px-6 lg:px-8">
                        <nav className="py-3" aria-label="Tabs">
                            <div
                                ref={scrollContainerRef}
                                className="flex overflow-x-auto space-x-1 pb-2 nav-pills-scroll"
                                style={{
                                    scrollBehavior: "smooth",
                                    WebkitOverflowScrolling: "touch"
                                }}
                            >
                                {tabItems.map((item) => {
                                    const isActive = item.key === currentTab;
                                    return (
                                        <a
                                            key={item.key}
                                            href={item.path}
                                            className={`
                                                flex-shrink-0 px-3 py-2 text-sm font-medium rounded-md
                                                transition-colors duration-200 ease-in-out
                                                whitespace-nowrap
                                                ${isActive
                                                    ? 'bg-[#740898] text-white'
                                                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                                                }
                                            `}
                                            role="tab"
                                            aria-selected={isActive}
                                            aria-current={isActive ? 'page' : undefined}
                                        >
                                            {item.label}
                                        </a>
                                    );
                                })}
                            </div>
                        </nav>
                    </div>
                </div>
            )}

            {/* Content - Adjust padding when tabs are hidden */}
            <div className={`flex-grow min-h-screen bg-[#f9fafb] ${hideHeader ? 'pt-0' : 'p-2 sm:p-4'}`}>
                {children}
            </div>
        </AppLayout>
        </>
    );
};

export default ProductsLayout;
