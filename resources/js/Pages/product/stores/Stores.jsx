import React, { useState, useEffect } from "react";
import { Button, Table, Row, Col, Input, message, Modal, Form, Space, Select } from "antd";
import { get, post } from "../../../axios";
import { EditOutlined, DeleteOutlined, SettingOutlined } from "@ant-design/icons";

const Stores = () => {
    // State for stores data
    const [stores, setStores] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Add debounce state
    const [searchTimeout, setSearchTimeout] = useState(null);

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // State for Shopify connection
    const [isShopifyModalVisible, setIsShopifyModalVisible] = useState(false);
    const [shopifyUrl, setShopifyUrl] = useState("");
    const [shopifyUrlError, setShopifyUrlError] = useState("");
    const [currentStoreForShopify, setCurrentStoreForShopify] = useState(null);

    // Fetch stores data
    const fetchStores = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            let endpoint = `channels?page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            console.log("response", response);
            if (response) {
                setStores(response.data || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch stores:", error);
            message.error("Failed to load stores");
        } finally {
            setLoading(false);
        }
    };

    // Load stores on component mount
    useEffect(() => {
        fetchStores(pagination.current, pagination.pageSize, searchText);
    }, []);

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [storeToDelete, setStoreToDelete] = useState(null);
    const [form] = Form.useForm();
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentStore, setCurrentStore] = useState(null);

    // Fetch store details by ID
    const fetchStoreDetails = async (storeId) => {
        try {
            setLoading(true);
            const id = storeId || null;
            if (!id) {
                message.error("Store ID is required");
                return;
            }

            const result = await get(`channels/${id}`);
            return result;
        } catch (error) {
            console.error("Failed to fetch store details:", error);
            message.error("Failed to load store details");
            return null;
        } finally {
            setLoading(false);
        }
    };

    // Handle create store
    const handleCreateStore = () => {
        // Reset form and state
        form.resetFields();
        setIsEditMode(false);
        setCurrentStore(null);

        // Open modal
        setIsModalVisible(true);
    };

    // Handle edit store
    const handleEdit = async (record) => {
        try {
            const storeId = record.id;
            const storeDetails = await fetchStoreDetails(storeId);

            if (storeDetails) {
                // Store the entire store object
                const store = storeDetails.channel || storeDetails;

                setCurrentStore(store);
                setIsEditMode(true);

                // Set form values
                const formValues = {
                    name: store.name,
                    language: store.language || "en",
                };

                form.setFieldsValue(formValues);
                setIsModalVisible(true);
            }
        } catch (error) {
            console.error("Error preparing store for edit:", error);
            message.error("Failed to load store for editing");
        }
    };

    // Open delete modal
    const openDeleteModal = (record) => {
        setStoreToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Handle delete store
    const handleDelete = async () => {
        try {
            setLoading(true);
            const id = storeToDelete.id || null;
            if (!id) {
                message.error("Store ID is required");
                return;
            }

            const response = await post(`channels/${id}`, {
                _method: "DELETE",
            });

            if (response) {
                message.success(`Store "${storeToDelete.name}" deleted successfully`);
                setIsDeleteModalVisible(false);
                fetchStores(pagination.current, pagination.pageSize, searchText);
            }
        } catch (error) {
            console.error("Failed to delete store:", error);
            message.error("Failed to delete store");
        } finally {
            setLoading(false);
        }
    };

    // Handle cancel delete
    const handleCancelDelete = () => {
        setIsDeleteModalVisible(false);
        setStoreToDelete(null);
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
    };

    // Handle search
    const handleSearch = (e) => {
        const searchValue = e.target.value;
        setSearchText(searchValue);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set a new timeout for the search (300ms debounce)
        const timeout = setTimeout(() => {
            fetchStores(1, pagination.pageSize, searchValue);
        }, 300);

        setSearchTimeout(timeout);
    };

    // Handle modal submit
    const handleModalSubmit = async () => {
        try {
            const values = await form.validateFields();

            setLoading(true);

            const formData = {
                name: values.name,
                language: values.language,
            };

            let response;

            if (isEditMode && currentStore) {
                // Update existing store
                formData.id = currentStore.id;
                response = await post(`channels/${currentStore.id}`, {
                    ...formData,
                    _method: "PUT",
                });

                if (response) {
                    message.success(`Store "${values.name}" updated successfully`);
                }
            } else {
                // Create new store
                response = await post("channels", formData);

                if (response) {
                    message.success(`Store "${values.name}" created successfully`);
                }
            }

            // Reset and close
            setIsModalVisible(false);
            form.resetFields();

            // Refresh the list
            fetchStores(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
            console.error("Failed to save store:", error);
            message.error("Failed to save store");
        } finally {
            setLoading(false);
        }
    };

    // Handle pagination change
    const handleTableChange = (pagination) => {
        setPagination(pagination);
        fetchStores(pagination.current, pagination.pageSize, searchText);
    };

    // Validate Shopify URL function
    const validateShopifyUrl = (url) => {
        if (!url || !url.trim()) {
            return false;
        }

        try {
            // Handle URLs with or without protocol
            let cleanUrl = url.trim();

            // Remove protocol if present (http://, https://)
            cleanUrl = cleanUrl.replace(/^https?:\/\//, "");

            // Remove www. if present
            cleanUrl = cleanUrl.replace(/^www\./, "");

            // Remove trailing slash if present
            cleanUrl = cleanUrl.replace(/\/$/, "");

            // Check if the URL matches Shopify store pattern
            const shopifyPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]*\.myshopify\.com$/;
            return shopifyPattern.test(cleanUrl);
        } catch (error) {
            return false;
        }
    };

    // Connect to Shopify - Open Modal
    const handleConnectToShopify = (record) => {
        setCurrentStoreForShopify(record);
        setShopifyUrl("");
        setShopifyUrlError("");
        setIsShopifyModalVisible(true);
    };

    // Handle Shopify Modal Cancel
    const handleShopifyModalCancel = () => {
        setIsShopifyModalVisible(false);
        setCurrentStoreForShopify(null);
        setShopifyUrl("");
    };

    // Handle Shopify Connect submission
    const handleShopifyConnect = () => {
        if (!shopifyUrl.trim()) {
            setShopifyUrlError("Please enter your Shopify store URL");
            return;
        }

        if (!validateShopifyUrl(shopifyUrl)) {
            setShopifyUrlError("Please enter a valid Shopify store URL (e.g., your-store.myshopify.com)");
            return;
        }

        // Process the Shopify URL to remove protocol
        const processedShopUrl = shopifyUrl.replace(/^https?:\/\//, "");

        // Prepare the query parameters
        const params = new URLSearchParams({
            shop: processedShopUrl,
            sync_product: "yes",
            should_not_billed: "1",
        });

        if (currentStoreForShopify) {
            params.append("channel_id", currentStoreForShopify.id);
        }

        // Construct the full URL
        const syncUrl = `/channel/shopify/install?${params.toString()}`;

        // Redirect to the constructed URL
        window.location.href = syncUrl;
    };

    // Update template
    const handleUpdateTemplate = async (record) => {
        try {
            setLoading(true);
            const response = await post(`channels/${record.id}/update-template`);

            if (response) {
                message.success(`Updated template for "${record.name}" successfully`);
                fetchStores(pagination.current, pagination.pageSize, searchText);
            }
        } catch (error) {
            console.error("Failed to update template:", error);
            message.error("Failed to update template");
        } finally {
            setLoading(false);
        }
    };

    // Styles for pagination
    const paginationOverrideStyles = `
        .ant-pagination-item-active {
            border-color: #740898 !important;
        }
        .ant-pagination-item-active a {
            color: #740898 !important;
        }
        .ant-pagination-item:hover {
            border-color: #740898 !important;
        }
        .ant-pagination-item:hover a {
            color: #740898 !important;
        }
        .ant-pagination-prev:hover .ant-pagination-item-link,
        .ant-pagination-next:hover .ant-pagination-item-link {
            color: #740898 !important;
            border-color: #740898 !important;
        }
        .ant-select-focused .ant-select-selector {
            border-color: #740898 !important;
        }
        .ant-select-item-option-selected {
            background-color: #f8f0fc !important;
        }
        .ant-select-item-option-active {
            background-color: #f8f0fc !important;
        }
        .ant-input-number-focused {
            border-color: #740898 !important;
        }
    `;

    // Define columns for the table
    const columns = [
        {
            title: "Name",
            dataIndex: "name",
            key: "name",
            ellipsis: true,
            sorter: (a, b) => a.name.localeCompare(b.name),
        },
        {
            title: "Status",
            key: "status",
            render: (_, record) => (
                <Button type="primary" onClick={() => handleConnectToShopify(record)} className="bg-white border-[#740898] text-[#740898]">
                    Connect to Shopify
                </Button>
            ),
        },
        {
            title: "Template",
            key: "template",
            render: (_, record) => (
                <Button onClick={() => handleUpdateTemplate(record)} className="bg-[#f0f0f0] text-[#252525]">
                    Update
                </Button>
            ),
        },
        {
            title: "Settings",
            key: "settings",
            render: (_, record) => (
                <Space size="middle">
                    <Button
                        type="text"
                        icon={<SettingOutlined />}
                        onClick={() => (window.location.href = `/channels/${record.id}/settings`)}
                    />
                </Space>
            ),
        },
    ];

    return (
        <div className="p-0 rounded-lg">
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Stores</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">Add and configure stores to expand your sales channels.</p>
                    </Col>
                    <Col>
                        <Button type="primary" className="bg-[#740898] border-[#740898] rounded font-medium" onClick={handleCreateStore}>
                            Create Store
                        </Button>
                    </Col>
                </Row>
            </div>

            {/* Search & Table Section */}
            <div className="bg-white rounded-lg overflow-hidden">
                <div className="py-4 px-6 border-b border-[#E5E7EB]">
                    <Row justify="space-between" align="middle">
                        <Col>
                            <h2 className="m-0 text-base font-medium text-[#252525]">Stores</h2>
                        </Col>
                        <Col>
                            <Input.Search placeholder="Search Stores" className="w-64" value={searchText} onChange={handleSearch} />
                        </Col>
                    </Row>
                </div>

                <Table
                    columns={columns}
                    dataSource={stores}
                    rowKey="id"
                    bordered={true}
                    loading={loading}
                    pagination={pagination}
                    onChange={handleTableChange}
                    className="custom-table"
                />
            </div>

            {/* Create/Edit Store Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">{isEditMode ? "Edit Store" : "Create a New Store"}</div>}
                open={isModalVisible}
                onCancel={handleModalCancel}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={640}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
            >
                {isEditMode ? null : (
                    <p className="text-[#6B7280] mb-4 mt-[-20px]">Enter the store details to expand your retail presence.</p>
                )}
                <Form form={form} layout="vertical" name="store_form" preserve={false} requiredMark={false}>
                    <Form.Item
                        name="name"
                        label={<span className="font-semibold text-[#252525] block mb-0">Store Name*</span>}
                        rules={[
                            {
                                required: true,
                                message: "Please enter store name",
                            },
                            {
                                pattern: /^[a-zA-Z0-9_ -]*$/,
                                message: "Name can only contain alphanumeric characters, spaces, underscores, and hyphens",
                            },
                        ]}
                    >
                        <Input placeholder="Enter Name" className="rounded-sm h-8" />
                    </Form.Item>

                    <Form.Item
                        name="language"
                        label={<span className="font-semibold text-[#252525] block mb-0">Language*</span>}
                        rules={[
                            {
                                required: true,
                                message: "Please select a language",
                            },
                        ]}
                    >
                        <Select
                            placeholder="Select Language"
                            className="w-full rounded-sm"
                            suffixIcon={
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                >
                                    <polyline points="6 9 12 15 18 9"></polyline>
                                </svg>
                            }
                            options={[
                                { value: "en", label: "English" },
                                { value: "es", label: "Spanish" },
                                { value: "fr", label: "French" },
                                { value: "de", label: "German" },
                                { value: "it", label: "Italian" },
                            ]}
                        />
                    </Form.Item>

                    <Form.Item className="m-0 text-right">
                        <Space size="middle">
                            <Button onClick={handleModalCancel} className="rounded min-w-20">
                                Cancel
                            </Button>
                            <Button type="primary" onClick={handleModalSubmit} className="bg-[#740898] border-[#740898] rounded min-w-20">
                                {isEditMode ? "Update" : "Save"}
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">Delete Store</div>}
                open={isDeleteModalVisible}
                onCancel={handleCancelDelete}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={480}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
            >
                <div className="mb-6">
                    <p className="text-[#252525]">
                        Are you sure you want to delete the store "<strong>{storeToDelete?.name}</strong>"? This action cannot be undone.
                    </p>
                </div>

                <div className="text-right">
                    <Space size="middle">
                        <Button onClick={handleCancelDelete} className="rounded min-w-20">
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            danger
                            onClick={handleDelete}
                            className="rounded min-w-20 bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700"
                        >
                            Delete
                        </Button>
                    </Space>
                </div>
            </Modal>

            {/* Shopify Connect Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">Connect Shopify Store</div>}
                open={isShopifyModalVisible}
                onCancel={handleShopifyModalCancel}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={640}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
            >
                <div className="space-y-4">
                    <p className="text-[#6B7280] mb-4">Enter your Shopify store URL to connect it with {currentStoreForShopify?.name}</p>
                    <Form layout="vertical">
                        <Form.Item
                            label={<span className="font-semibold text-[#252525] block mb-0">Shop URL*</span>}
                            validateStatus={shopifyUrlError ? "error" : ""}
                            help={shopifyUrlError}
                        >
                            <Input
                                value={shopifyUrl}
                                onChange={(e) => {
                                    const newValue = e.target.value;
                                    setShopifyUrl(newValue);

                                    if (!newValue.trim()) {
                                        setShopifyUrlError("Please enter your Shopify store URL");
                                    } else if (!validateShopifyUrl(newValue)) {
                                        setShopifyUrlError("Please enter a valid Shopify store URL (e.g., your-store.myshopify.com)");
                                    } else {
                                        setShopifyUrlError("");
                                    }
                                }}
                                placeholder="your-shop-url.myshopify.com"
                                className="rounded-sm"
                            />
                            <p className="text-[#626262] text-[14px] font-[400] mt-[8px]">
                                Haven't created a store yet? Learn more on&nbsp;
                                <a
                                    href="https://www.shopify.com"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-[#740898] underline"
                                >
                                    shopify.com
                                </a>
                            </p>
                        </Form.Item>
                        <div className="flex justify-end gap-4">
                            <Button onClick={handleShopifyModalCancel} className="rounded min-w-20">
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                onClick={handleShopifyConnect}
                                disabled={!shopifyUrl.trim() || shopifyUrlError !== ""}
                                className="bg-[#740898] border-[#740898] rounded min-w-20"
                            >
                                Connect
                            </Button>
                        </div>
                    </Form>
                </div>
            </Modal>
        </div>
    );
};

export default Stores;
