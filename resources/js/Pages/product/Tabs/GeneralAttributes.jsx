import React, { useState, useRef, useMemo, useEffect } from "react";
import { Input, Radio, Select, Tree, InputNumber } from "antd"; // Import InputNumber
import JoditEditor from "jodit-react"; // Import Jodit Editor
import { get } from "../../../axios";
const { Option } = Select;
const { TreeNode } = Tree;

const GeneralAttributes = ({ product, productId, onDataChange, formData, setFormData }) => {
    const editor = useRef(null);
    const [status, setStatus] = useState(formData.status || "active");
    const [productIdentifier, setProductIdentifier] = useState(formData.product_name || "");
    const [productName, setProductName] = useState(formData.product_name || "");
    const [description, setDescription] = useState(formData.description || "");
    const [brand, setBrand] = useState(formData.brand?.[0] || "Select Brand");
    const [vendor, setVendor] = useState(formData.vendor?.[0] || "Select Vendor");
    const [category, setCategory] = useState(product?.categories?.map((cat) => cat.id.toString()) || []);
    const [store, setStore] = useState(formData.channels || []);
    const [language, setLanguage] = useState(product?.versions?.[0]?.id?.toString() || "EN-US");
    const [versionId, setVersionId] = useState(product?.versions?.[0]?.id?.toString() || "");
    const [isLoading, setIsLoading] = useState(false);

    // State for storing API data
    const [brands, setBrands] = useState([]);
    const [vendors, setVendors] = useState([]);
    const [categories, setCategories] = useState([]);
    const [stores, setStores] = useState([]);
    const [languages, setLanguages] = useState([]);
    const [loading, setLoading] = useState({
        brands: false,
        vendors: false,
        categories: false,
        stores: false,
        languages: false,
    });

    // New state variables for missing fields
    const [sku, setSku] = useState("");
    const [barcode, setBarcode] = useState("");
    const [weight, setWeight] = useState("");
    const [weightUnit, setWeightUnit] = useState("oz");
    const [price, setPrice] = useState("");
    const [compareAtPrice, setCompareAtPrice] = useState("");
    const [costPrice, setCostPrice] = useState("");

    // Fetch product data based on productId
    useEffect(() => {
        const fetchProductData = async () => {
            if (!productId) return;

            setIsLoading(true);
            try {
                const response = await get(`/products/${productId}?type=general`);
                const productData = response.product;

                // Set status (convert to lowercase for radio buttons)
                setStatus(productData?.status?.toLowerCase() === "draft" ? "draft" : "active");

                // Set product identifier from handle
                setProductIdentifier(productData.handle || "");

                // Set product name
                setProductName(productData.product_name || "");

                // Find description in values array if it exists
                const descriptionValue = productData.values?.find(
                    (item) => item.attribute_id === 45 // Assuming 45 is the ID for description
                );
                setDescription(descriptionValue?.value || "");

                // Set brand if available
                if (productData.brands?.length > 0) {
                    setBrand(productData.brands[0].id.toString());
                }

                // Set vendors if available
                if (productData.vendors?.length > 0) {
                    setVendor(productData.vendors[0].id?.toString() || "");
                }

                // Set categories
                if (productData.categories?.length > 0) {
                    setCategory(productData?.categories?.map((cat) => cat.id.toString()));
                }

                // Set stores/channels
                if (productData.channels?.length > 0) {
                    setStore(productData.channels.map((ch) => ch.id.toString()));
                }

                // Set language/version
                if (productData.versions?.length > 0) {
                    setLanguage(productData.versions[0].name || "EN-US");
                }

                // Set new fields if available
                setSku(productData.sku || "");
                setBarcode(productData.barcode || "");
                setWeight(productData.weight || "");
                setPrice(productData.price || "");
                setCompareAtPrice(productData.compare_at_price || "");
                setCostPrice(productData.cost_price || "");
            } catch (error) {
                console.error("Error fetching product data:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchProductData();
    }, [productId]);

    // Initialize with provided product data if available
    console.log("product", product);

    useEffect(() => {
        if (product) {
            // Set status (convert to lowercase for radio buttons)
            setStatus(product?.status?.toLowerCase() === "draft" ? "draft" : "active");

            // Set product identifier from handle
            setProductIdentifier(product.handle || "");

            // Set product name
            setProductName(product.product_name || "");

            // Find description in values array if it exists
            const descriptionValue = product.values?.find(
                (item) => item.attribute_id === 45 // Assuming 45 is the ID for description
            );
            setDescription(descriptionValue?.value || "");

            // Set brand if available
            if (product.brands?.length > 0) {
                setBrand(product.brands[0].id.toString());
            }

            // Set vendors if available
            if (product.vendors?.length > 0) {
                setVendor(product.vendors[0].id?.toString() || "");
            }

            // Set categories
            if (product.categories?.length > 0) {
                setCategory(product?.categories?.map((cat) => cat.id.toString()));
            }

            // Set stores/channels
            if (product.channels?.length > 0) {
                setStore(product.channels.map((ch) => ch.id.toString()));
            }

            // Set language/version
            if (product.versions?.length > 0) {
                setLanguage(product.versions[0].name || "EN-US");
            }

            // Set new fields if available
            setSku(product.sku || "");
            setBarcode(product.barcode || "");
            setWeight(product.weight || "");
            setPrice(product.price || "");
            setCompareAtPrice(product.compare_at_price || "");
            setCostPrice(product.cost_price || "");
        }
    }, [product]);

    // Fetch data from APIs
    useEffect(() => {
        const fetchBrands = async () => {
            setLoading((prev) => ({ ...prev, brands: true }));
            try {
                const response = await get("/brands");
                setBrands(response.data);
                if (response.data.length > 0 && !brand) {
                    setBrand(response.data[0].id.toString());
                }
            } catch (error) {
                console.error("Error fetching brands:", error);
            } finally {
                setLoading((prev) => ({ ...prev, brands: false }));
            }
        };

        const fetchVendors = async () => {
            setLoading((prev) => ({ ...prev, vendors: true }));
            try {
                const response = await get("/vendors");
                setVendors(response.data);
                if (response.data.length > 0 && !vendor) {
                    setVendor(response.data[0].id.toString());
                }
            } catch (error) {
                console.error("Error fetching vendors:", error);
            } finally {
                setLoading((prev) => ({ ...prev, vendors: false }));
            }
        };

        const fetchCategories = async () => {
            setLoading((prev) => ({ ...prev, categories: true }));
            try {
                const response = await get("/categories?tree_structure=true");
                setCategories(response.data);
            } catch (error) {
                console.error("Error fetching categories:", error);
            } finally {
                setLoading((prev) => ({ ...prev, categories: false }));
            }
        };

        const fetchStores = async () => {
            setLoading((prev) => ({ ...prev, stores: true }));
            try {
                const response = await get("/channels");
                setStores(response.data);
            } catch (error) {
                console.error("Error fetching stores:", error);
            } finally {
                setLoading((prev) => ({ ...prev, stores: false }));
            }
        };

        const fetchLanguages = async () => {
            setLoading((prev) => ({ ...prev, languages: true }));
            try {
                const response = await get("/versions");
                setLanguages(response.data);
                if (response.data.length > 0) {
                    setLanguage(response.data[0].code || "EN-US");
                }
            } catch (error) {
                console.error("Error fetching languages:", error);
            } finally {
                setLoading((prev) => ({ ...prev, languages: false }));
            }
        };

        fetchBrands();
        fetchVendors();
        fetchCategories();
        fetchStores();
        fetchLanguages();
    }, []);

    // Memoize the config to prevent unnecessary re-renders
    const config = useMemo(
        () => ({
            readonly: false,
            height: 200,
            buttons: [
                "source",
                "|",
                "bold",
                "italic",
                "underline",
                "strikethrough",
                "|",
                "ul",
                "ol",
                "|",
                "font",
                "fontsize",
                "paragraph",
                "|",
                "align",
                "|",
                "link",
                "image",
                "|",
                "hr",
                "table",
                "|",
                "undo",
                "redo",
                "|",
                "eraser",
                "fullsize",
            ],
            uploader: {
                insertImageAsBase64URI: true,
            },
            removeButtons: ["about", "file"],
            showXPathInStatusbar: false,
            showCharsCounter: false,
            showWordsCounter: false,
            toolbarAdaptive: false,
            style: {
                background: "#ffffff",
                color: "#252525",
            },
            enableDragAndDropFileToEditor: true,
            saveFocus: true,
            spellcheck: true,
            editorCssClass: "editor-content",
            defaultMode: "1",
            askBeforePasteHTML: false,
            askBeforePasteFromWord: false,
            disablePlugins: ["paste-from-word"],
            events: {
                afterInit: (instance) => {
                    instance.workplace.tabIndex = 1;
                },
                focus: (instance) => {
                    instance.selection.focus();
                },
            },
            preserveSelection: true,
        }),
        []
    );

    // Modified function to get all children IDs of a category
    const getAllChildrenIds = (categoryId, categoriesData) => {
        const childrenIds = [];
        const findChildren = (id) => {
            const category = categoriesData.find((cat) => cat.id === id.toString());
            if (category && category.children && category.children.length > 0) {
                category.children.forEach((child) => {
                    childrenIds.push(child.name);
                    findChildren(child.name);
                });
            }
        };

        findChildren(categoryId);
        return childrenIds;
    };

    // Transform categories data for Tree component
    const transformCategoriesToTreeData = (categories) => {
        return categories.map((cat) => ({
            title: cat.name,
            key: cat.id,
            children: cat.children && cat.children.length > 0 ? transformCategoriesToTreeData(cat.children) : undefined,
        }));
    };

    // Computed tree data for categories
    const categoryTreeData = useMemo(() => {
        return transformCategoriesToTreeData(categories);
    }, [categories]);

    // Update formData when any field changes
    useEffect(() => {
        onDataChange({
            status,
            product_name: productName,
            description,
            brand: [brand],
            vendor: [vendor],
            category: category.join(","),
            channels: store,
            sku: formData.sku,
            barcode: formData.barcode,
            weight: formData.weight,
            weight_unit: formData.weight_unit,
            price: formData.price,
            compare_at_price: formData.compare_at_price,
            cost_price: formData.cost_price,
            version_id: versionId,
        });
    }, [status, productName, description, brand, vendor, category, store, versionId]);

    // Handlers that trigger onDataChange
    const handleStatusChange = (e) => {
        setStatus(e.target.value);
        onDataChange?.();
    };

    const handleProductIdentifierChange = (e) => {
        setProductIdentifier(e.target.value);
        onDataChange?.();
    };

    const handleProductNameChange = (e) => {
        setProductName(e.target.value);
        onDataChange?.();
    };

    const handleDescriptionChange = (newContent) => {
        if (editor.current?.editor) {
            setDescription(newContent);
            onDataChange?.();
        }
    };

    const handleBrandChange = (value) => {
        setBrand(value);
        onDataChange?.();
    };

    const handleVendorChange = (value) => {
        setVendor(value);
        onDataChange?.();
    };

    const handleCategoryChange = (value) => {
        let selectedCategories = [...value];
        const previouslySelected = new Set(category);
        selectedCategories.forEach((catId) => {
            if (!previouslySelected.has(catId)) {
                const childrenIds = getAllChildrenIds(catId, categories);
                childrenIds.forEach((childId) => {
                    if (!selectedCategories.includes(childId)) {
                        selectedCategories.push(childId);
                    }
                });
            }
        });
        setCategory(selectedCategories);
        onDataChange?.();
    };

    const handleStoreChange = (value) => {
        setStore(value);
        onDataChange?.();
    };

    const handleLanguageChange = (value) => {
        setLanguage(value);
        // Find the selected version from languages array
        const selectedVersion = languages.find((lang) => lang.code === value || lang.id.toString() === value);
        if (selectedVersion) {
            setVersionId(selectedVersion.id.toString());
        }
        onDataChange?.();
    };

    // Add handlers for new fields
    const handleSkuChange = (e) => {
        setSku(e.target.value);
        onDataChange({
            sku: e.target.value,
        });
    };

    const handleBarcodeChange = (e) => {
        setBarcode(e.target.value);
        onDataChange({
            barcode: e.target.value,
        });
    };

    const handleWeightChange = (value) => {
        setWeight(value);
        onDataChange({
            weight: value,
        });
    };

    const handleWeightUnitChange = (value) => {
        setWeightUnit(value);
        onDataChange({
            weight_unit: value,
        });
    };

    const handlePriceChange = (value) => {
        setPrice(value);
        onDataChange({
            price: value,
        });
    };

    const handleCompareAtPriceChange = (value) => {
        setCompareAtPrice(value);
        onDataChange({
            compare_at_price: value,
        });
    };

    const handleCostPriceChange = (value) => {
        setCostPrice(value);
        onDataChange({
            cost_price: value,
        });
    };

    // Render tree nodes recursively
    const renderTreeNodes = (data) => {
        return data.map((item) => {
            if (item.children) {
                return (
                    <TreeNode title={item.title} key={item.key}>
                        {renderTreeNodes(item.children)}
                    </TreeNode>
                );
            }
            return <TreeNode title={item.title} key={item.key} />;
        });
    };

    // Add a helper function to find a category title by its value in the nested structure
    const findCategoryNameById = (id, categoriesData) => {
        let name = id;

        const searchInCategories = (cats) => {
            for (const cat of cats) {
                if (cat.id === id) {
                    name = cat.name;
                    return true;
                }
                if (cat.children && cat.children.length > 0) {
                    if (searchInCategories(cat.children)) {
                        return true;
                    }
                }
            }
            return false;
        };

        searchInCategories(categoriesData);
        return name;
    };

    return (
        <div className="flex flex-col 2xl:flex-row p-5">
            {/* Left Column (Full width on smaller screens, 70% on 2xl) */}
            <div className="w-full 2xl:w-7/12 2xl:pr-4 mb-0 2xl:mb-0">
                {/* Status Section */}
                <div className="mb-4 flex items-center">
                    <span className="font-semibold text-lg mr-[16px] -mt-[5px] text-[#252525]">Status</span>
                    <Radio.Group onChange={handleStatusChange} value={status} className="flex gap-2">
                        <Radio
                            value="draft"
                            style={{ height: "36px" }}
                            className={`flex items-center border rounded-[4px] p-2 ${
                                status === "draft" ? "border-[#740898]" : "border-[#DBDBDB]"
                            }`}
                        >
                            <span className="mr-2">Draft</span>
                        </Radio>
                        <Radio
                            value="active"
                            style={{ height: "36px" }}
                            className={`flex items-center border rounded-[4px] p-2 ${
                                status === "active" ? "border-[#740898]" : "border-[#DBDBDB]"
                            }`}
                        >
                            <span className="mr-2">Active</span>
                        </Radio>
                    </Radio.Group>
                </div>

                {/* Input Fields Section */}
                <div className="mb-4">
                    <label className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${productIdentifier ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                        <span className="font-semibold text-[14px] text-[#252525]">Product Identifier</span>
                    </label>
                    <Input value={productIdentifier} onChange={handleProductIdentifierChange} placeholder="Enter Product Identifier" />
                </div>

                <div className="mb-4">
                    <label className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${productName ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                        <span className="font-semibold text-[14px] text-[#252525]">Product Name</span>
                    </label>
                    <Input value={productName} onChange={handleProductNameChange} placeholder="Enter Product Name" />
                </div>

                {/* Group SKU, UPC/Barcode, and Weight in one row */}
                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    {/* SKU field */}
                    <div className="flex-1">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${sku ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">SKU</span>
                        </label>
                        <Input value={sku} onChange={handleSkuChange} placeholder="Enter SKU" />
                    </div>

                    {/* UPC/Barcode field */}
                    <div className="flex-1">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${barcode ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">UPC/Barcode</span>
                        </label>
                        <Input value={barcode} onChange={handleBarcodeChange} placeholder="Enter UPC/Barcode" />
                    </div>

                    {/* Weight field with unit selector */}
                    <div className="flex-1">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${weight ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Weight</span>
                        </label>
                        <div className="flex">
                            <Select
                                value={weightUnit}
                                onChange={handleWeightUnitChange}
                                style={{
                                    width: 60,
                                    marginRight: 0,
                                    borderRadius: "0px",
                                    backgroundColor: "#F9FAFB !important",
                                    borderColor: "#DBDBDB",
                                }}
                                className="weight-select-custom"
                            >
                                <Option value="oz">oz</Option>
                                <Option value="lb">lb</Option>
                                <Option value="g">g</Option>
                                <Option value="kg">kg</Option>
                            </Select>
                            <InputNumber
                                className="flex-grow rounded-l-none"
                                value={weight}
                                onChange={handleWeightChange}
                                placeholder="Enter Weight"
                                min={0}
                                precision={2}
                            />
                        </div>
                    </div>
                </div>

                {/* Group Price, Compare at Price, and Cost Price in one row */}
                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    {/* Price field */}
                    <div className="flex-1">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${price ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Price</span>
                        </label>
                        <div className="flex items-center">
                            <span className="px-3 h-[32px] w-[60px] py-1 border border-[#DBDBDB] bg-[#F9FAFB] rounded-l-md rounded-r-none border-r-0">
                                USD
                            </span>
                            <InputNumber
                                className="flex-grow rounded-l-none"
                                value={price}
                                onChange={handlePriceChange}
                                placeholder="0.00"
                                min={0}
                                precision={2}
                            />
                        </div>
                    </div>

                    {/* Compare at price field */}
                    <div className="flex-1">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${compareAtPrice ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Compare at price</span>
                        </label>
                        <div className="flex items-center">
                            <span className="px-3 h-[32px] w-[60px] py-1 border border-[#DBDBDB] bg-[#F9FAFB] rounded-l-md rounded-r-none border-r-0">
                                USD
                            </span>
                            <InputNumber
                                className="flex-grow rounded-l-none"
                                value={compareAtPrice}
                                onChange={handleCompareAtPriceChange}
                                placeholder="0.00"
                                min={0}
                                precision={2}
                            />
                        </div>
                    </div>

                    {/* Cost price field */}
                    <div className="flex-1">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${costPrice ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Cost Price</span>
                        </label>
                        <div className="flex items-center">
                            <span className="px-3 h-[32px] w-[60px] py-1 border border-[#DBDBDB] bg-[#F9FAFB] rounded-l-md rounded-r-none border-r-0">
                                USD
                            </span>
                            <InputNumber
                                className="flex-grow rounded-l-none"
                                value={costPrice}
                                onChange={handleCostPriceChange}
                                placeholder="0.00"
                                min={0}
                                precision={2}
                            />
                        </div>
                    </div>
                </div>

                <div className="mb-4">
                    <label className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${description ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                        <span className="font-semibold text-[14px] text-[#252525]">Description</span>
                    </label>
                    <div className="editor-container">
                        <JoditEditor
                            ref={editor}
                            value={description}
                            config={config}
                            onChange={handleDescriptionChange}
                            className="border rounded-md"
                        />
                    </div>
                </div>
            </div>

            {/* Right Column (Full width on smaller screens, 30% on 2xl) */}
            <div className="w-full 2xl:w-5/12 2xl:pl-4">
                {/* Language Dropdown */}
                <div className="mb-4">
                    <Select
                        value={language}
                        onChange={handleLanguageChange}
                        style={{ backgroundColor: "#FCF5FF" }}
                        loading={loading.languages}
                    >
                        {languages.map((lang) => (
                            <Option key={lang.id} value={lang.id.toString()}>
                                {lang.code || lang.name}
                            </Option>
                        ))}
                        {languages.length === 0 && !loading.languages && (
                            <>
                                <Option value="EN-US">EN-US</Option>
                                <Option value="EN-UK">EN-UK</Option>
                                <Option value="FR-FR">FR-FR</Option>
                            </>
                        )}
                    </Select>
                </div>

                {/* Dropdown Fields Container */}
                <div className="border border-[#DBDBDB] rounded-[4px] p-4">
                    {/* Brand Dropdown */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${brand ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Brand</span>
                        </label>
                        <Select value={brand} onChange={handleBrandChange} style={{ width: "100%" }} loading={loading.brands}>
                            {brands?.map((b) => (
                                <Option key={b.id} value={b.id.toString()}>
                                    {b.name}
                                </Option>
                            ))}
                            {brands?.length === 0 && !loading.brands && (
                                <>
                                    <Option value="Brand 1">Brand 1</Option>
                                    <Option value="Brand 2">Brand 2</Option>
                                </>
                            )}
                        </Select>
                    </div>

                    {/* Vendor Dropdown */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${vendor ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Vendor</span>
                        </label>
                        <Select value={vendor} onChange={handleVendorChange} style={{ width: "100%" }} loading={loading.vendors}>
                            {vendors.map((v) => (
                                <Option key={v.id} value={v.id.toString()}>
                                    {v.title}
                                </Option>
                            ))}
                            {vendors.length === 0 && !loading.vendors && (
                                <>
                                    <Option value="Vendor 1">Vendor 1</Option>
                                    <Option value="Vendor 2">Vendor 2</Option>
                                </>
                            )}
                        </Select>
                    </div>

                    {/* Category Dropdown with Tree Structure */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${category.length > 0 ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Category</span>
                        </label>
                        <Select
                            mode="multiple"
                            value={category}
                            onChange={handleCategoryChange}
                            style={{ width: "100%" }}
                            loading={loading.categories}
                            showSearch
                            dropdownStyle={{ maxHeight: "250px", overflow: "auto" }}
                            listHeight={250}
                            dropdownRender={(menu) => (
                                <div>
                                    <div className="p-2">
                                        {categories.length > 0 && !loading.categories && (
                                            <Tree
                                                checkable
                                                onCheck={(checkedKeys) => handleCategoryChange(checkedKeys)}
                                                checkedKeys={category}
                                                treeData={categoryTreeData}
                                                selectable={false}
                                                height={200}
                                            />
                                        )}
                                    </div>
                                </div>
                            )}
                        >
                            {categories.map((cat) => (
                                <Option key={cat.id} value={cat.id.toString()}>
                                    {cat.name}
                                </Option>
                            ))}
                        </Select>
                        {category.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                                {category.map((catId) => {
                                    const categoryObj = categories.find((cat) => cat.id.toString() === catId);
                                    return (
                                        <span
                                            key={catId}
                                            className="px-[12px] py-[8.5px] bg-[#F1E6F5] text-[#740898] font-semibold text-[14px] rounded-[8px]"
                                        >
                                            {categoryObj?.name || catId}
                                        </span>
                                    );
                                })}
                            </div>
                        )}
                    </div>

                    {/* Store Dropdown */}
                    <div className="mb-4">
                        <label className="flex items-center">
                            <span className={`w-2 h-2 rounded-full mr-2 ${store.length > 0 ? "bg-[#15D476]" : "bg-[#FE1F23]"}`}></span>
                            <span className="font-semibold text-[14px] text-[#252525]">Store</span>
                        </label>
                        <Select
                            mode="multiple"
                            value={store}
                            onChange={handleStoreChange}
                            style={{ width: "100%" }}
                            loading={loading.stores}
                        >
                            {stores.map((st) => (
                                <Option key={st.id} value={st.id.toString()}>
                                    {st.name}
                                </Option>
                            ))}
                            {stores.length === 0 && !loading.stores && (
                                <>
                                    <Option value="Store 1">Store 1</Option>
                                    <Option value="Store 2">Store 2</Option>
                                </>
                            )}
                        </Select>
                        {store.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                                {store.map((storeId) => {
                                    const storeObj = stores.find((s) => s.id.toString() === storeId) || { name: storeId };
                                    return (
                                        <span
                                            key={storeId}
                                            className="px-[12px] py-[8.5px] bg-[#F1E6F5] text-[#740898] font-semibold text-[14px] rounded-[8px]"
                                        >
                                            {storeObj.name}
                                        </span>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default GeneralAttributes;
