import React, { useState, useEffect } from "react";
import { Table, Input, Button, Row, Col, Tooltip, Popconfirm, message, Modal, Form, Select, Space } from "antd";
import { SearchOutlined, FilterOutlined, DeleteOutlined, EditOutlined, DownOutlined } from "@ant-design/icons";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";
import { get, post } from "../../../axios";

// Add CSS for overriding the blue outline/border on pagination buttons
const paginationOverrideStyles = `
    .ant-pagination li,
    .ant-pagination button,
    .ant-pagination .ant-pagination-item-link,
    .ant-pagination-item a {
        outline: none !important;
    }

    .ant-pagination li:focus,
    .ant-pagination li:focus-visible,
    .ant-pagination button:focus,
    .ant-pagination button:focus-visible,
    .ant-pagination-item-link:focus,
    .ant-pagination-item-link:focus-visible {
        outline: none !important;
        box-shadow: none !important;
        border-color: inherit;
    }
`;

const AttributeSets = () => {
    // State for attribute sets data
    const [attributeSets, setAttributeSets] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Add debounce state
    const [searchTimeout, setSearchTimeout] = useState(null);

    // State for all available attributes (for the form)
    const [allAttributes, setAllAttributes] = useState([]);

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch attribute sets data
    const fetchAttributeSets = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            let endpoint = `families?page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            console.log("response", response);
            if (response) {
                setAttributeSets(response.data || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch attribute sets:", error);
            message.error("Failed to load attribute sets");
        } finally {
            setLoading(false);
        }
    };

    // Fetch all attributes for the form
    const fetchAttributes = async () => {
        try {
            const response = await get("attributes?paginate=10");
            if (response && response.attribute) {
                // Filter out default attributes and type 13 (as done in the backend)
                const filteredAttributes = response.attribute.filter((attr) => !attr.is_default && attr.attribute_type_id !== 13);
                setAllAttributes(filteredAttributes);
            }
        } catch (error) {
            console.error("Failed to fetch attributes:", error);
            message.error("Failed to load attributes");
        }
    };

    // Fetch attribute set details by ID
    const fetchAttributeSetDetails = async (attributeSetId) => {
        try {
            setLoading(true);
            const id = attributeSetId || null;
            if (!id) {
                message.error("Attribute Set ID is required");
                return;
            }

            const result = await get(`families/${id}`);
            return result;
        } catch (error) {
            console.error("Failed to fetch attribute set details:", error);
            message.error("Failed to load attribute set details");
            return null;
        } finally {
            setLoading(false);
        }
    };

    // Load attribute sets on component mount
    useEffect(() => {
        fetchAttributeSets(pagination.current, pagination.pageSize, searchText);
        fetchAttributes();
    }, []);

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [attributeSetToDelete, setAttributeSetToDelete] = useState(null);
    const [form] = Form.useForm();
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentAttributeSet, setCurrentAttributeSet] = useState(null);

    // Handle create attribute set
    const handleCreateAttributeSet = () => {
        // Reset form and state
        form.resetFields();
        setIsEditMode(false);
        setCurrentAttributeSet(null);

        // Open modal
        setIsModalVisible(true);
    };

    // Handle edit attribute set
    const handleEdit = async (record) => {
        try {
            const attributeSetId = record.id;
            const attributeSetDetails = await fetchAttributeSetDetails(attributeSetId);

            if (attributeSetDetails) {
                // Store the entire attribute set object
                const attributeSet = attributeSetDetails.family || attributeSetDetails;

                setCurrentAttributeSet(attributeSet);
                setIsEditMode(true);

                // Set form values
                const formValues = {
                    name: attributeSet.name,
                };

                form.setFieldsValue(formValues);
                setIsModalVisible(true);
            }
        } catch (error) {
            console.error("Error preparing attribute set for edit:", error);
            message.error("Failed to load attribute set for editing");
        }
    };

    // Open delete modal
    const openDeleteModal = (record) => {
        setAttributeSetToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Handle delete attribute set
    const handleDelete = async () => {
        if (attributeSetToDelete) {
            try {
                setLoading(true);
                const attributeSetId = attributeSetToDelete.id;

                if (!attributeSetId) {
                    message.error("Failed to delete attribute set: Missing attribute set ID");
                    return;
                }

                // Make the delete API call using post with _method: DELETE (Laravel convention)
                const response = await post(`families/${attributeSetId}`, {
                    _method: "DELETE",
                });

                if (response) {
                    message.success(`Attribute set "${attributeSetToDelete.name}" deleted successfully`);
                    fetchAttributeSets(pagination.current, pagination.pageSize, searchText); // Refresh data after delete
                }

                setIsDeleteModalVisible(false);
                setAttributeSetToDelete(null);
            } catch (error) {
                console.error("Failed to delete attribute set:", error);

                // Display appropriate error message
                if (error.response && error.response.data && error.response.data.message) {
                    message.error(`Failed to delete attribute set: ${error.response.data.message}`);
                } else {
                    message.error("Failed to delete attribute set. Please try again.");
                }
            } finally {
                setLoading(false);
            }
        }
    };

    // Handle delete modal cancel
    const handleDeleteCancel = () => {
        setIsDeleteModalVisible(false);
        setAttributeSetToDelete(null);
    };

    // Handle search
    const handleSearch = (e) => {
        const searchValue = e.target.value;
        setSearchText(searchValue);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Set a new timeout for the search (300ms debounce)
        const timeout = setTimeout(() => {
            fetchAttributeSets(1, pagination.pageSize, searchValue);
        }, 300);

        setSearchTimeout(timeout);
    };

    // Handle modal submit
    const handleModalSubmit = async () => {
        try {
            const values = await form.validateFields();

            setLoading(true);

            const formData = {
                name: values.name,
            };

            let response;

            if (isEditMode && currentAttributeSet) {
                // Update existing attribute set
                formData.id = currentAttributeSet.id;
                response = await post(`families/${currentAttributeSet.id}`, {
                    ...formData,
                    _method: "PUT",
                });

                if (response) {
                    message.success(`Attribute set "${values.name}" updated successfully`);
                }
            } else {
                // Create new attribute set
                response = await post("families", formData);

                if (response) {
                    message.success(`Attribute set "${values.name}" created successfully`);
                }
            }

            // Reset and close
            setIsModalVisible(false);
            form.resetFields();

            // Refresh the list
            fetchAttributeSets(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
            console.error("Form submission error:", error);

            if (error.errorFields) {
                // Form validation error
                return;
            }

            message.error("Failed to save attribute set. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setIsEditMode(false);
        setCurrentAttributeSet(null);
    };

    // Table columns configuration
    const columns = [
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-5">Name</span>,
            dataIndex: "name",
            key: "name",
            width: "20%",
            render: (text) => <span className="text-[#252525] pl-3">{text}</span>,
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">Attributes</span>,
            dataIndex: "attributes",
            key: "attributes",
            width: "70%",
            render: (attributes) => (
                <span>
                    {attributes && attributes.length > 0
                        ? attributes
                              .slice(0, 5)
                              .map((attr) => attr.name)
                              .join(", ") + (attributes.length > 5 ? "..." : "")
                        : "No attributes"}
                </span>
            ),
        },
        {
            title: <span className="text-[#626262] font-[400] pl-4">Action</span>,
            key: "action",
            width: "10%",
            render: (_, record) => (
                <div className="flex gap-1">
                    {
                        <>
                            <Tooltip title="Delete">
                                <Button
                                    type="text"
                                    icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                    onClick={() => openDeleteModal(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                            <Tooltip title="Edit">
                                <Button
                                    type="text"
                                    icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                    onClick={() => handleEdit(record)}
                                    className="p-0"
                                />
                            </Tooltip>
                        </>
                    }
                </div>
            ),
        },
    ];

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchAttributeSets(newPagination.current, newPagination.pageSize, searchText);
    };
    console.log("attributeSets", attributeSets);
    return (
        <div className="p-0 rounded-lg">
            <style>{paginationOverrideStyles}</style>

            {/* Header Section */}
            <div className="mb-6">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h1 className="m-0 text-xl font-semibold text-[#252525]">Manage Attribute Sets</h1>
                        <p className="mt-1 mb-0 text-sm text-[#6B7280]">
                            Organize your product attributes into sets for easier management.
                        </p>
                    </Col>
                    <Col>
                        <Button
                            type="primary"
                            className="bg-[#740898] border-[#740898] rounded font-medium"
                            onClick={handleCreateAttributeSet}
                        >
                            Create Attribute Set
                        </Button>
                    </Col>
                </Row>
            </div>

            <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
                {/* Table Controls */}
                <div className="p-5">
                    <Row justify="space-between" align="middle">
                        <Col>
                            <h2 className="m-0 text-base font-semibold text-[#252525]">Attribute Sets</h2>
                        </Col>
                        <Col>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Search Attribute Set"
                                    prefix={<SearchOutlined />}
                                    value={searchText}
                                    onChange={handleSearch}
                                    className="w-60 rounded"
                                    allowClear
                                />
                            </div>
                        </Col>
                    </Row>
                </div>

                {/* Attribute Sets Table */}
                <div className="border-t border-[#DBDBDB] rounded-b-lg overflow-hidden">
                    <Table
                        columns={columns}
                        dataSource={attributeSets}
                        rowKey="id"
                        pagination={{
                            ...pagination,
                            position: ["bottomRight"],
                            className: "mt-4 text-right pagination-no-focus-border",
                            itemRender: (page, type, originalElement) => {
                                if (type === "page") {
                                    return (
                                        <Button
                                            size="small"
                                            className={`rounded outline-none focus:outline-none bg-white ${
                                                pagination.current === page
                                                    ? "text-[#740898] border-[#740898] shadow-none"
                                                    : "border-[#d9d9d9]"
                                            }`}
                                            style={{
                                                boxShadow: "none",
                                                margin: "0 4px",
                                            }}
                                        >
                                            {page}
                                        </Button>
                                    );
                                }
                                return originalElement;
                            },
                        }}
                        loading={loading}
                        onChange={handleTableChange}
                        className="attribute-sets-table"
                        locale={{
                            emptyText: "No attribute sets found",
                        }}
                        bordered={true}
                        size="middle"
                        components={{
                            header: {
                                cell: (props) => (
                                    <th
                                        {...props}
                                        style={{
                                            ...props.style,
                                            backgroundColor: "#F9FAFB",
                                            padding: 0,
                                            height: "48px",
                                        }}
                                    />
                                ),
                            },
                            body: {
                                row: (props) => (
                                    <tr
                                        {...props}
                                        style={{
                                            ...props.style,
                                            height: "48px",
                                        }}
                                    />
                                ),
                            },
                        }}
                    />
                </div>
            </div>

            {/* Create/Edit Modal */}
            <Modal
                title={
                    <div className="text-2xl font-bold text-[#252525]">
                        {isEditMode ? "Edit Attribute Set" : "Create a New Attribute Set"}
                    </div>
                }
                open={isModalVisible}
                onCancel={handleModalCancel}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={640}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
            >
                <Form form={form} layout="vertical" name="attribute_set_form" preserve={false} requiredMark={false}>
                    <Form.Item
                        name="name"
                        label={<span className="font-semibold text-[#252525] block mb-0">Attribute Set Name*</span>}
                        rules={[
                            {
                                required: true,
                                message: "Please enter attribute set name",
                            },
                            {
                                pattern: /^[a-zA-Z0-9_ -]*$/,
                                message: "Name can only contain alphanumeric characters, spaces, underscores, and hyphens",
                            },
                        ]}
                    >
                        <Input placeholder="Enter Name" className="rounded-sm h-8" />
                    </Form.Item>

                    <Form.Item className="m-0 text-right">
                        <Space size="middle">
                            <Button onClick={handleModalCancel} className="rounded min-w-20">
                                Cancel
                            </Button>
                            <Button type="primary" onClick={handleModalSubmit} className="bg-[#740898] border-[#740898] rounded min-w-20">
                                {isEditMode ? "Update" : "Save"}
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">Delete Attribute Set</div>}
                open={isDeleteModalVisible}
                onCancel={handleDeleteCancel}
                footer={null}
                closeIcon={null}
                className="rounded-xl"
                width={480}
                centered
                maskClosable={true}
                bodyStyle={{ paddingTop: "12px" }}
            >
                <div className="mb-6">
                    <p className="text-[#252525]">
                        Are you sure you want to delete the attribute set "<strong>{attributeSetToDelete?.name}</strong>"?
                    </p>
                </div>

                <div className="text-right">
                    <Space size="middle">
                        <Button onClick={handleDeleteCancel} className="rounded min-w-20">
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            danger
                            onClick={handleDelete}
                            className="rounded min-w-20 bg-red-600 border-red-600 hover:bg-red-700 hover:border-red-700"
                        >
                            Delete
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
};

export default AttributeSets;
