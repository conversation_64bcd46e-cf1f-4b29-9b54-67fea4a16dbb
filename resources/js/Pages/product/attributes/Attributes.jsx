import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import {
    Table,
    Input,
    Button,
    Row,
    Col,
    Tooltip,
    message,
    Modal,
    Form,
    Select,
    Checkbox,
    Radio,
    Spin,
    DatePicker,
    InputNumber,
    Rate,
    Switch,
    ColorPicker,
} from "antd";
import { SearchOutlined } from "@ant-design/icons";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";
import { get, post } from "../../../axios";
import moment from "moment";

const Attributes = forwardRef((props, ref) => {
    // State for attributes data
    const [attributes, setAttributes] = useState([]);
    const [loading, setLoading] = useState(false);
    const [selectedAttributeType, setSelectedAttributeType] = useState(null);
    const [selectedUnit, setSelectedUnit] = useState(null);
    const [attributeFamilies, setAttributeFamilies] = useState([]);

    // State for search text
    const [searchText, setSearchText] = useState("");
    const [isSearching, setIsSearching] = useState(false);
    const [searchTimeout, setSearchTimeout] = useState(null);

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch attributes data
    const fetchAttributes = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            setIsSearching(!!searchQuery);
            let endpoint = `attributes?page=${page}&paginate=${pageSize}`;

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            if (response && response.data) {
                setAttributes(response.data || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch attributes:", error);
            message.error("Failed to load attributes");
        } finally {
            setLoading(false);
        }
    };

    // Load attributes on component mount
    useEffect(() => {
        fetchAttributes(pagination.current, pagination.pageSize, searchText);
        fetchAttributeFamilies();
    }, []);

    // Fetch attribute families
    const fetchAttributeFamilies = async () => {
        try {
            const response = await get("families");
            if (response && response.data) {
                setAttributeFamilies(response.data);
            }
        } catch (error) {
            console.error("Failed to fetch attribute families:", error);
            message.error("Failed to load attribute sets");
        }
    };

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [attributeToDelete, setAttributeToDelete] = useState(null);
    const [form] = Form.useForm();
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentAttribute, setCurrentAttribute] = useState(null);

    // Handle attribute type change
    const handleAttributeTypeChange = (value) => {
        setSelectedAttributeType(value);
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        handleCreateAttribute: () => {
            // Reset form and state
            form.resetFields();
            setIsEditMode(false);
            setCurrentAttribute(null);
            setSelectedAttributeType(null);

            // Make sure attribute families are loaded
            if (attributeFamilies.length === 0) {
                fetchAttributeFamilies();
            }

            // Open modal
            setIsModalVisible(true);
        },
    }));

    // Handle edit attribute
    const handleEdit = async (record) => {
        try {
            setLoading(true);
            setCurrentAttribute(record);

            // Make sure attribute families are loaded
            if (attributeFamilies.length === 0) {
                await fetchAttributeFamilies();
            }

            // Fetch complete attribute data
            const response = await get(`attributes/${record.id}`);
            if (!response || !response.attribute) {
                throw new Error("Failed to fetch attribute details");
            }

            const attributeData = response.attribute;
            setIsEditMode(true);
            setSelectedAttributeType(attributeData.attribute_type_id);

            // Default form values
            const formValues = {
                attributeTitle: attributeData.name,
                attributeType: attributeData.attribute_type_id,
                attributeSet: attributeData.attribute_families?.[0]?.id || null,
                description: attributeData.description || "",
                isRequired: attributeData.is_required === 1,
                valueType: attributeData.value_type === "list" ? "multiple" : "single",
            };

            // Add attribute type specific values
            switch (parseInt(attributeData.attribute_type_id)) {
                case 1: // Single line text
                case 2: // Multi line text
                    formValues.regex = attributeData.regular_expression || "";
                    formValues.minLength = attributeData.min_length || null;
                    formValues.maxLength = attributeData.max_length || null;
                    break;
                case 3: // Number
                    formValues.numberType = attributeData.type || "integer";
                    formValues.minValue = attributeData.min || null;
                    formValues.maxValue = attributeData.max || null;
                    break;
                case 4: // List
                    // Extract option values from attribute_options array
                    formValues.options = attributeData.attribute_options?.map((option) => option.name) || [];
                    break;
                case 5: // Date and time
                    formValues.dateType = attributeData.start_date_time ? "datetime" : "date";
                    if (formValues.dateType === "date") {
                        formValues.minDate = attributeData.start_date ? moment(attributeData.start_date) : null;
                        formValues.maxDate = attributeData.end_date ? moment(attributeData.end_date) : null;
                    } else {
                        formValues.minDate = attributeData.start_date_time ? moment(attributeData.start_date_time) : null;
                        formValues.maxDate = attributeData.end_date_time ? moment(attributeData.end_date_time) : null;
                    }
                    break;
                case 6: // Measurement
                    formValues.measurementType = attributeData.measurement_type || "weight";
                    formValues.unit = attributeData.min_unit || null;
                    formValues.minMeasurement = attributeData.min || null;
                    formValues.maxMeasurement = attributeData.max || null;
                    // Set unit state for the dropdown
                    setSelectedUnit(attributeData.min_unit || null);
                    break;
                case 7: // Rating
                    formValues.maxRating = attributeData.max_rating || 5;
                    formValues.minValue = attributeData.min || 0;
                    break;
                default:
                    break;
            }

            // Set form values
            form.setFieldsValue(formValues);

            // Open modal
            setIsModalVisible(true);
        } catch (error) {
            console.error("Failed to fetch attribute details:", error);
            message.error("Failed to load attribute details for editing");
        } finally {
            setLoading(false);
        }
    };

    // Open delete confirmation modal
    const openDeleteModal = (record) => {
        setAttributeToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Confirm delete attribute
    const confirmDelete = async () => {
        try {
            setLoading(true);
            // API call to delete attribute
            await post(`attributes/${attributeToDelete.id}`, { _method: "DELETE" });
            message.success("Attribute deleted successfully");
            fetchAttributes(pagination.current, pagination.pageSize, searchText);
            setIsDeleteModalVisible(false);
        } catch (error) {
            console.error("Failed to delete attribute:", error);
            message.error("Failed to delete attribute");
        } finally {
            setLoading(false);
        }
    };

    // Handle search input change
    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchText(value);

        // Clear any existing timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // If search is cleared immediately fetch all results
        if (value === "") {
            setIsSearching(false);
            fetchAttributes(1, pagination.pageSize, "");
            return;
        }

        // Set a new timeout to debounce the search request
        const timeoutId = setTimeout(() => {
            // Reset to first page when searching
            setIsSearching(!!value);
            fetchAttributes(1, pagination.pageSize, value);
        }, 500); // 500ms debounce

        setSearchTimeout(timeoutId);
    };

    // Handle search button click (no longer needed with debounced search)
    const onSearch = () => {
        fetchAttributes(1, pagination.pageSize, searchText);
    };

    // Handle form submission
    const handleFormSubmit = async () => {
        try {
            setLoading(true);

            // Validate form fields
            const values = await form.validateFields();
            console.log("Form values:", values); // Debug log

            // Prepare form data
            const formData = {
                name: values.attributeTitle,
                attribute_type_id: values.attributeType,
                attribute_family: values.attributeSet ? [values.attributeSet] : [],
                description: values.description || null,
                value_type: values.valueType === "multiple" ? "list" : "single",
                regular_expression: values.regex || null,
            };

            // Add attribute type specific data
            switch (parseInt(values.attributeType)) {
                case 3: // Number
                    formData.type = values.numberType || "integer";
                    formData.min = values.minValue || null;
                    formData.max = values.maxValue || null;
                    break;
                case 4: // List
                    formData.attribute_options = (values.options || []).map((option) => ({
                        name: option,
                    }));
                    break;
                case 5: // Date and time
                    if (values.dateType === "date") {
                        formData.start_date = values.minDate ? values.minDate.format("YYYY-MM-DD") : null;
                        formData.end_date = values.maxDate ? values.maxDate.format("YYYY-MM-DD") : null;
                    } else {
                        formData.start_date_time = values.minDate ? values.minDate.format("YYYY-MM-DD HH:mm:ss") : null;
                        formData.end_date_time = values.maxDate ? values.maxDate.format("YYYY-MM-DD HH:mm:ss") : null;
                    }
                    break;
                case 6: // Measurement
                    formData.min = values.minMeasurement || null;
                    formData.max = values.maxMeasurement || null;
                    formData.min_unit = values.unit || selectedUnit || null;
                    formData.max_unit = values.unit || selectedUnit || null;
                    break;
                case 7: // Rating
                    formData.max_rating = values.maxRating;
                    formData.min = values.minValue || 0;
                    break;
                default:
                    break;
            }

            // Send request to API
            if (isEditMode && currentAttribute) {
                await post(`attributes/${currentAttribute.id}`, { ...formData, _method: "PUT" });
                message.success("Attribute updated successfully");
            } else {
                await post("attributes", formData);
                message.success("Attribute created successfully");
            }

            // Close modal and refresh data
            setIsModalVisible(false);
            fetchAttributes(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
            console.error("Form submission error:", error);

            if (error.errorFields) {
                // Form validation error
                return;
            }

            message.error("Failed to save attribute. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setIsEditMode(false);
        setCurrentAttribute(null);
        setSelectedAttributeType(null);
    };

    // Attribute type options
    const attributeTypes = {
        1: "Single line text",
        2: "Multi line text",
        3: "Number",
        4: "List",
        5: "Date and time",
        6: "Measurement",
        7: "Rating",
        8: "JSON",
        9: "True or false",
        10: "URL",
        11: "Color",
    };

    // Table columns configuration
    const columns = [
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-5">Name</span>,
            dataIndex: "name",
            key: "name",
            width: "25%",
            render: (text) => <span className="text-[#252525] pl-3">{text}</span>,
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">Attribute Type</span>,
            dataIndex: "attribute_type",
            key: "attribute_type",
            width: "25%",
            render: (record) => {
                return <span>{record.name}</span>;
            },
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">Attribute Set</span>,
            dataIndex: "attribute_families",
            key: "attribute_families",
            width: "25%",
            render: (record) => {
                // This would ideally come from a map of set IDs to names
                return <span>{record.map((set) => set.name).join(", ")}</span>;
            },
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px] pl-2">Is Required</span>,
            dataIndex: "is_required",
            key: "is_required",
            width: "15%",
            render: (isRequired) => <Checkbox checked={isRequired === 1} disabled />,
        },
        {
            title: <span className="text-[#626262] font-[400] pl-4">Action</span>,
            key: "action",
            width: "10%",
            render: (_, record) => (
                <div className="flex gap-1">
                    <Tooltip title="Delete">
                        <Button
                            type="text"
                            icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                            onClick={() => openDeleteModal(record)}
                            className="p-0"
                        />
                    </Tooltip>
                    <Tooltip title="Edit">
                        <Button
                            type="text"
                            icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                            onClick={() => handleEdit(record)}
                            className="p-0"
                        />
                    </Tooltip>
                </div>
            ),
        },
    ];

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchAttributes(newPagination.current, newPagination.pageSize, searchText);
    };

    // Render dynamic form fields based on attribute type
    const renderAttributeTypeFields = () => {
        switch (selectedAttributeType) {
            case 1: // Single line text
            case 2: // Multi line text
                return (
                    <>
                        <div className="mb-3 p-3 bg-[#F9FAFB] rounded border border-[#EAECF0]">
                            <div className="font-[700] text-[14px] text-[#252525] mb-2">Validation</div>
                            <div className="text-sm text-gray-600 mb-3">Values can contain letters, numbers, and special characters.</div>

                            <Form.Item
                                name="regex"
                                label={<span className="font-[700] text-[14px] text-[#252525]">Regular Expression</span>}
                                className="mb-2"
                            >
                                <Input placeholder="e.g /^[\\da-zA-Z1-9-]+\\$/" />
                            </Form.Item>

                            <Row gutter={12}>
                                <Col span={12}>
                                    <Form.Item
                                        name="minLength"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Minimum Character Length</span>}
                                        className="mb-0"
                                    >
                                        <InputNumber placeholder="Enter min length" min={0} className="w-full" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="maxLength"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Maximum Character Length</span>}
                                        className="mb-0"
                                    >
                                        <InputNumber placeholder="Enter max length" min={0} className="w-full" />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                    </>
                );

            case 3: // Number
                return (
                    <>
                        <div className="mb-3 p-3 bg-[#F9FAFB] rounded border border-[#EAECF0]">
                            <div className="font-[700] text-[14px] text-[#252525] mb-2">Validation</div>
                            <div className="mb-2 text-sm text-gray-600">Values must be numbers with a decimal.</div>

                            <Form.Item
                                name="numberType"
                                label={<span className="font-[700] text-[14px] text-[#252525]">Select Type</span>}
                                initialValue="integer"
                                className="mb-2"
                            >
                                <Radio.Group>
                                    <Radio value="integer">Integer</Radio>
                                    <Radio value="decimal">Decimal</Radio>
                                    <Radio value="price">Price</Radio>
                                </Radio.Group>
                            </Form.Item>

                            <Row gutter={12}>
                                <Col span={12}>
                                    <Form.Item
                                        name="minValue"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Minimum Value</span>}
                                        className="mb-0"
                                    >
                                        <InputNumber placeholder="Min value" className="w-full" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="maxValue"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Maximum Value</span>}
                                        className="mb-0"
                                    >
                                        <InputNumber placeholder="Max value" className="w-full" />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                    </>
                );

            case 4: // List
                return (
                    <>
                        <div className="mb-3 p-3 bg-[#F9FAFB] rounded border border-[#EAECF0]">
                            <div className="font-[700] text-[14px] text-[#252525] mb-2">Validation</div>
                            <div className="text-sm text-gray-600">Values can contain letters, numbers, and special characters.</div>

                            <div className="mt-3">
                                <div className="font-[700] text-[14px] text-[#252525] mb-2">Manage Options (Values of your attribute)</div>
                                <Form.List name="options">
                                    {(fields, { add, remove }) => (
                                        <>
                                            {fields.map(({ key, name, ...restField }) => (
                                                <div key={key} className="flex mb-2">
                                                    <Form.Item
                                                        {...restField}
                                                        name={name}
                                                        className="mb-0 flex-1"
                                                        rules={[{ required: true, message: "Please enter option value" }]}
                                                    >
                                                        <Input placeholder="Enter option value" />
                                                    </Form.Item>
                                                    <Button
                                                        type="text"
                                                        className="ml-2"
                                                        onClick={() => remove(name)}
                                                        icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                    />
                                                </div>
                                            ))}
                                            <Button
                                                type="primary"
                                                onClick={() => add()}
                                                className="bg-[#740898] hover:bg-[#8a0eb7] border-[#740898] mt-1"
                                            >
                                                Add Option Value
                                            </Button>
                                        </>
                                    )}
                                </Form.List>
                            </div>
                        </div>
                    </>
                );

            case 5: // Date and time
                return (
                    <>
                        <div className="mb-3 p-3 bg-[#F9FAFB] rounded border border-[#EAECF0]">
                            <div className="font-[700] text-[14px] text-[#252525] mb-2">Validation</div>
                            <div className="text-sm text-gray-600 mb-2">Values must be dates.</div>

                            <Form.Item
                                name="dateType"
                                label={<span className="font-[700] text-[14px] text-[#252525]">Select Type</span>}
                                initialValue="date"
                                className="mb-2"
                            >
                                <Radio.Group onChange={(e) => form.setFieldsValue({ minDate: null, maxDate: null })}>
                                    <Radio value="date">Date</Radio>
                                    <Radio value="datetime">Date and time</Radio>
                                </Radio.Group>
                            </Form.Item>

                            <Row gutter={12}>
                                <Col span={12}>
                                    <Form.Item
                                        name="minDate"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Minimum Date</span>}
                                        dependencies={["dateType"]}
                                        className="mb-0"
                                    >
                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) => prevValues.dateType !== currentValues.dateType}
                                        >
                                            {({ getFieldValue }) => {
                                                const dateType = getFieldValue("dateType");
                                                return (
                                                    <DatePicker
                                                        showTime={dateType === "datetime"}
                                                        placeholder={dateType === "datetime" ? "dd/mm/yyyy hh:mm" : "dd/mm/yyyy"}
                                                        format={dateType === "datetime" ? "DD/MM/YYYY HH:mm" : "DD/MM/YYYY"}
                                                        className="w-full"
                                                    />
                                                );
                                            }}
                                        </Form.Item>
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="maxDate"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Maximum Date</span>}
                                        dependencies={["dateType"]}
                                        className="mb-0"
                                    >
                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) => prevValues.dateType !== currentValues.dateType}
                                        >
                                            {({ getFieldValue }) => {
                                                const dateType = getFieldValue("dateType");
                                                return (
                                                    <DatePicker
                                                        showTime={dateType === "datetime"}
                                                        placeholder={dateType === "datetime" ? "dd/mm/yyyy hh:mm" : "dd/mm/yyyy"}
                                                        format={dateType === "datetime" ? "DD/MM/YYYY HH:mm" : "DD/MM/YYYY"}
                                                        className="w-full"
                                                    />
                                                );
                                            }}
                                        </Form.Item>
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                    </>
                );

            case 6: // Measurement
                return (
                    <>
                        <div className="mb-3 p-3 bg-[#F9FAFB] rounded border border-[#EAECF0]">
                            <div className="font-[700] text-[14px] text-[#252525] mb-2">Validation</div>
                            <div className="text-sm text-gray-600 mb-2">Values must be numbers.</div>

                            <Form.Item
                                name="measurementType"
                                label={<span className="font-[700] text-[14px] text-[#252525]">Select Type</span>}
                                initialValue="weight"
                                className="mb-2"
                            >
                                <Radio.Group>
                                    <Radio value="weight">Weight</Radio>
                                    <Radio value="volume">Volume</Radio>
                                    <Radio value="dimension">Dimension</Radio>
                                </Radio.Group>
                            </Form.Item>

                            <Form.Item
                                shouldUpdate={(prevValues, currentValues) => prevValues.measurementType !== currentValues.measurementType}
                            >
                                {({ getFieldValue }) => {
                                    const measurementType = getFieldValue("measurementType") || "weight";
                                    let options = [];

                                    if (measurementType === "weight") {
                                        options = [
                                            { value: "kg", label: "Kilograms (kg)" },
                                            { value: "g", label: "Grams (g)" },
                                            { value: "lb", label: "Pounds (lb)" },
                                            { value: "oz", label: "Ounces (oz)" },
                                        ];
                                    } else if (measurementType === "volume") {
                                        options = [
                                            { value: "l", label: "Liters (l)" },
                                            { value: "ml", label: "Milliliters (ml)" },
                                            { value: "gal", label: "Gallons (gal)" },
                                            { value: "oz_fl", label: "Fluid Ounces (fl oz)" },
                                        ];
                                    } else if (measurementType === "dimension") {
                                        options = [
                                            { value: "m", label: "Meters (m)" },
                                            { value: "cm", label: "Centimeters (cm)" },
                                            { value: "mm", label: "Millimeters (mm)" },
                                            { value: "in", label: "Inches (in)" },
                                            { value: "ft", label: "Feet (ft)" },
                                        ];
                                    }

                                    return (
                                        <Form.Item
                                            name="unit"
                                            label={<span className="font-[700] text-[14px] text-[#252525]">Unit</span>}
                                            rules={[{ required: true, message: "Please select a unit" }]}
                                            className="mb-2"
                                        >
                                            <Select
                                                placeholder="Select unit"
                                                value={selectedUnit}
                                                onChange={(value) => {
                                                    console.log("Unit selected:", value);
                                                    setSelectedUnit(value);
                                                    form.setFieldsValue({ unit: value });
                                                }}
                                            >
                                                {options.map((option) => (
                                                    <Select.Option key={option.value} value={option.value}>
                                                        {option.label}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    );
                                }}
                            </Form.Item>

                            <Row gutter={12}>
                                <Col span={12}>
                                    <Form.Item
                                        name="minMeasurement"
                                        label={
                                            <Form.Item
                                                noStyle
                                                shouldUpdate={(prevValues, currentValues) =>
                                                    prevValues.measurementType !== currentValues.measurementType
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    const type = getFieldValue("measurementType") || "weight";
                                                    return (
                                                        <span className="font-[700] text-[14px] text-[#252525]">
                                                            Minimum{" "}
                                                            {type === "weight" ? "Weight" : type === "volume" ? "Volume" : "Dimension"}
                                                        </span>
                                                    );
                                                }}
                                            </Form.Item>
                                        }
                                        className="mb-0"
                                    >
                                        <InputNumber placeholder="Enter minimum value" className="w-full" />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="maxMeasurement"
                                        label={
                                            <Form.Item
                                                noStyle
                                                shouldUpdate={(prevValues, currentValues) =>
                                                    prevValues.measurementType !== currentValues.measurementType
                                                }
                                            >
                                                {({ getFieldValue }) => {
                                                    const type = getFieldValue("measurementType") || "weight";
                                                    return (
                                                        <span className="font-[700] text-[14px] text-[#252525]">
                                                            Maximum{" "}
                                                            {type === "weight" ? "Weight" : type === "volume" ? "Volume" : "Dimension"}
                                                        </span>
                                                    );
                                                }}
                                            </Form.Item>
                                        }
                                        className="mb-0"
                                    >
                                        <InputNumber placeholder="Enter maximum value" className="w-full" />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                    </>
                );

            case 7: // Rating
                return (
                    <>
                        <div className="mb-3 p-3 bg-[#F9FAFB] rounded border border-[#EAECF0]">
                            <div className="font-[700] text-[14px] text-[#252525] mb-2">Validation</div>
                            <div className="text-sm text-gray-600 mb-2">Values must be numbers.</div>

                            <Row gutter={12}>
                                <Col span={12}>
                                    <Form.Item
                                        name="minValue"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Minimum Value</span>}
                                        className="mb-0"
                                    >
                                        <InputNumber placeholder="Min value" className="w-full" min={0} />
                                    </Form.Item>
                                </Col>
                                <Col span={12}>
                                    <Form.Item
                                        name="maxRating"
                                        label={<span className="font-[700] text-[14px] text-[#252525]">Maximum Value</span>}
                                        className="mb-0"
                                        initialValue={5}
                                    >
                                        <InputNumber placeholder="Max value" className="w-full" min={1} max={10} />
                                    </Form.Item>
                                </Col>
                            </Row>
                        </div>
                    </>
                );

            case 8: // JSON
                return <></>;

            case 10: // URL
                return <></>;

            default:
                return null;
        }
    };

    return (
        <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
            {/* Table Controls */}
            <div className="p-5">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h2 className="m-0 text-base font-semibold text-[#252525]">Attributes</h2>
                    </Col>
                    <Col>
                        <div className="flex gap-2">
                            <Input
                                placeholder="Search Attribute"
                                prefix={<SearchOutlined />}
                                value={searchText}
                                onChange={handleSearch}
                                className="w-60 rounded"
                                allowClear
                                onPressEnter={() => fetchAttributes(1, pagination.pageSize, searchText)}
                            />
                        </div>
                    </Col>
                </Row>
            </div>

            {/* Attributes Table */}
            <div className="border-t border-[#DBDBDB] rounded-b-lg overflow-hidden">
                {isSearching && searchText && (
                    <div className="p-3 bg-[#F9FAFB] border-b border-[#DBDBDB]">
                        <span className="text-[#252525]">
                            Search results for: <strong>{searchText}</strong>
                        </span>
                        <Button
                            type="link"
                            onClick={() => {
                                setSearchText("");
                                setIsSearching(false);
                                fetchAttributes(1, pagination.pageSize, "");
                            }}
                            className="text-[#740898] ml-2 p-0"
                        >
                            Clear
                        </Button>
                    </div>
                )}
                <Table
                    columns={columns}
                    dataSource={attributes}
                    rowKey="id"
                    pagination={{
                        ...pagination,
                        position: ["bottomRight"],
                        className: "mt-4 text-right pagination-no-focus-border",
                        itemRender: (page, type, originalElement) => {
                            if (type === "page") {
                                return (
                                    <Button
                                        size="small"
                                        className={`rounded outline-none focus:outline-none bg-white ${
                                            pagination.current === page ? "text-[#740898] border-[#740898] shadow-none" : "border-[#d9d9d9]"
                                        }`}
                                        style={{
                                            boxShadow: "none",
                                            margin: "0 4px",
                                        }}
                                    >
                                        {page}
                                    </Button>
                                );
                            }
                            return originalElement;
                        },
                    }}
                    loading={{
                        indicator: <Spin size="default" />,
                        spinning: loading,
                    }}
                    onChange={handleTableChange}
                    className="attributes-table"
                    locale={{
                        emptyText: "No attributes found",
                    }}
                    bordered={true}
                    size="middle"
                    components={{
                        header: {
                            cell: (props) => (
                                <th
                                    {...props}
                                    style={{
                                        ...props.style,
                                        backgroundColor: "#F9FAFB",
                                        padding: 0,
                                        height: "48px",
                                    }}
                                />
                            ),
                        },
                        body: {
                            row: (props) => (
                                <tr
                                    {...props}
                                    style={{
                                        ...props.style,
                                        height: "48px",
                                    }}
                                />
                            ),
                        },
                    }}
                />
            </div>

            {/* Create/Edit Modal */}
            <Modal
                title={<div className="text-2xl font-bold text-[#252525]">{isEditMode ? "Edit Attribute" : "Create a New Attribute"}</div>}
                open={isModalVisible}
                onCancel={handleModalCancel}
                closeIcon={null}
                footer={[
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        loading={loading}
                        onClick={handleFormSubmit}
                        className="bg-[#740898] hover:bg-[#8a0eb7] border-[#740898]"
                    >
                        {isEditMode ? "Update" : "Save"}
                    </Button>,
                ]}
                width={640}
                centered
            >
                <div className="mb-3">Choose the attribute type and enter the details to add a new product attribute.</div>
                <Form form={form} layout="vertical" className="space-y-3">
                    <Form.Item
                        name="attributeType"
                        label={<span className="font-[700] text-[14px] text-[#252525]">Attribute Type*</span>}
                        rules={[{ required: true, message: "Please select attribute type" }]}
                        className="mb-2"
                    >
                        <Select
                            placeholder="Select attribute type"
                            onChange={handleAttributeTypeChange}
                            dropdownStyle={{ height: "auto" }}
                            listHeight={500}
                        >
                            {Object.entries(attributeTypes).map(([id, name]) => (
                                <Select.Option key={id} value={Number(id)}>
                                    {name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>

                    <Form.Item
                        name="attributeSet"
                        label={<span className="font-[700] text-[14px] text-[#252525]">Attribute Set*</span>}
                        rules={[{ required: true, message: "Please select attribute set" }]}
                        className="mb-2"
                    >
                        <Select placeholder="Select attribute set" loading={attributeFamilies.length === 0}>
                            {attributeFamilies.map((family) => (
                                <Select.Option key={family.id} value={family.id}>
                                    {family.name}
                                </Select.Option>
                            ))}
                        </Select>
                    </Form.Item>

                    <Form.Item
                        name="attributeTitle"
                        label={<span className="font-[700] text-[14px] text-[#252525]">Attribute Title*</span>}
                        rules={[{ required: true, message: "Please enter attribute title" }]}
                        className="mb-2"
                    >
                        <Input placeholder="Enter title" />
                    </Form.Item>

                    <Form.Item
                        name="description"
                        label={<span className="font-[700] text-[14px] text-[#252525]">Description</span>}
                        className="mb-2"
                    >
                        <Input placeholder="Enter description" />
                    </Form.Item>

                    {/* Value Type (Single/Multiple) */}
                    {selectedAttributeType && [1, 3, 4, 6, 7, 10, 11].includes(selectedAttributeType) && (
                        <Form.Item
                            name="valueType"
                            label={<span className="font-[700] text-[14px] text-[#252525]">Value Type</span>}
                            className="mb-3"
                            initialValue="single"
                        >
                            <Radio.Group>
                                <Radio value="single">Single Value</Radio>
                                <Radio value="multiple">Multiple Values</Radio>
                            </Radio.Group>
                        </Form.Item>
                    )}

                    {/* Conditional fields based on attribute type */}
                    {selectedAttributeType && renderAttributeTypeFields()}

                    <Form.Item name="isRequired" valuePropName="checked" className="mb-1">
                        <Checkbox className="font-[700] text-[14px] text-[#252525]">Is Required?</Checkbox>
                    </Form.Item>
                </Form>
            </Modal>

            {/* Delete Confirmation Modal */}
            <Modal
                title="Delete Attribute"
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                closeIcon={null}
                footer={[
                    <Button key="cancel" onClick={() => setIsDeleteModalVisible(false)}>
                        Cancel
                    </Button>,
                    <Button key="delete" type="primary" danger loading={loading} onClick={confirmDelete}>
                        Delete
                    </Button>,
                ]}
                width={400}
                centered
            >
                <p>Are you sure you want to delete this attribute?</p>
                {attributeToDelete && <p className="font-semibold">{attributeToDelete.name}</p>}
                <p>This action cannot be undone.</p>
            </Modal>
        </div>
    );
});

export default Attributes;
