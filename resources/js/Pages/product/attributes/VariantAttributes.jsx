import React, { useState, useEffect, forwardRef, useImperative<PERSON><PERSON>le } from "react";
import { Table, Input, Button, Row, Col, Tooltip, message, Modal, Form, Select, Checkbox, Spin } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import EditIcon from "../../../../../public/v2/icons/edit-icon.svg";
import DeleteIcon from "../../../../../public/v2/icons/delete-icon.svg";
import { get, post } from "../../../axios";

// Import the main Attributes component to access its modal
import Attributes from "./Attributes";

const VariantAttributes = forwardRef((props, ref) => {
    // State for variant attributes data
    const [attributes, setAttributes] = useState([]);
    const [loading, setLoading] = useState(false);

    // State for search text
    const [searchText, setSearchText] = useState("");

    // Reference to the main attributes component
    const attributesRef = React.useRef();

    // State for pagination
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
        defaultPageSize: 10,
        showQuickJumper: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
    });

    // Fetch variant attributes data
    const fetchAttributes = async (page = 1, pageSize = 10, searchQuery = "") => {
        try {
            setLoading(true);
            // Endpoint would be adjusted for variant attributes
            let endpoint = `attributes?page=${page}&paginate=${pageSize}&attribute_type_id=3`; // Assuming type 3 is for variants

            // Add search query if provided
            if (searchQuery) {
                endpoint += `&name=${encodeURIComponent(searchQuery)}`;
            }

            const response = await get(endpoint);
            if (response && response.data) {
                setAttributes(response.data || []);

                // Update pagination from response
                if (response.pagination) {
                    setPagination({
                        ...pagination,
                        current: response.pagination.current_page,
                        pageSize: response.pagination.per_page,
                        total: response.pagination.total,
                    });
                }
            }
        } catch (error) {
            console.error("Failed to fetch variant attributes:", error);
            message.error("Failed to load variant attributes");
        } finally {
            setLoading(false);
        }
    };

    // Load variant attributes on component mount
    useEffect(() => {
        fetchAttributes(pagination.current, pagination.pageSize, searchText);
    }, []);

    // Form states
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
    const [attributeToDelete, setAttributeToDelete] = useState(null);
    const [form] = Form.useForm();
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentAttribute, setCurrentAttribute] = useState(null);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
        handleCreateAttribute: () => {
            // Use the main Attributes component's modal
            if (attributesRef.current) {
                attributesRef.current.handleCreateAttribute();
            }
        },
    }));

    // Handle edit attribute
    const handleEdit = (record) => {
        // Use the main Attributes component's modal
        if (attributesRef.current) {
            attributesRef.current.handleCreateAttribute();
        }
    };

    // Open delete confirmation modal
    const openDeleteModal = (record) => {
        setAttributeToDelete(record);
        setIsDeleteModalVisible(true);
    };

    // Confirm delete variant attribute
    const confirmDelete = async () => {
        try {
            setLoading(true);
            // API call to delete attribute
            await post(`attributes/${attributeToDelete.id}`, { _method: "DELETE" });
            message.success("Variant attribute deleted successfully");
            fetchAttributes(pagination.current, pagination.pageSize, searchText);
            setIsDeleteModalVisible(false);
        } catch (error) {
            console.error("Failed to delete variant attribute:", error);
            message.error("Failed to delete variant attribute");
        } finally {
            setLoading(false);
        }
    };

    // Handle search input change
    const handleSearch = (e) => {
        const value = e.target.value;
        setSearchText(value);
        if (value === "") {
            // If search is cleared, fetch all variant attributes
            fetchAttributes(1, pagination.pageSize, "");
        }
    };

    // Handle search
    const onSearch = () => {
        fetchAttributes(1, pagination.pageSize, searchText);
    };

    // Handle form submission
    const handleFormSubmit = async () => {
        try {
            setLoading(true);

            // Validate form fields
            const values = await form.validateFields();

            // Prepare form data
            const formData = {
                name: values.name,
                attribute_type_id: values.attributeType || 3, // Default to variant type
                attribute_set_id: values.attributeSet,
                is_required: values.isRequired ? 1 : 0,
                handle: values.name.toLowerCase().replace(/[^a-z0-9]/g, "_"), // Generate handle from name
            };

            // Send request to API
            if (isEditMode && currentAttribute) {
                await post(`attributes/${currentAttribute.id}`, { ...formData, _method: "PUT" });
                message.success("Variant attribute updated successfully");
            } else {
                await post("attributes", formData);
                message.success("Variant attribute created successfully");
            }

            // Close modal and refresh data
            setIsModalVisible(false);
            fetchAttributes(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
            console.error("Form submission error:", error);

            if (error.errorFields) {
                // Form validation error
                return;
            }

            message.error("Failed to save variant attribute. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    // Handle modal cancel
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setIsEditMode(false);
        setCurrentAttribute(null);
    };

    // Table columns configuration
    const columns = [
        {
            title: <span className="text-[#626262] font-[400] text-[14px]">Name</span>,
            dataIndex: "name",
            key: "name",
            width: "75%",
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px]">Is Required</span>,
            dataIndex: "is_required",
            key: "is_required",
            width: "15%",
            render: (isRequired) => <Checkbox checked={isRequired === 1} disabled />,
        },
        {
            title: <span className="text-[#626262] font-[400] text-[14px]">Action</span>,
            key: "action",
            width: "10%",
            render: (_, record) => (
                <div className="flex gap-2">
                    <Tooltip title="Delete">
                        <Button
                            type="text"
                            icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                            onClick={() => openDeleteModal(record)}
                            className="p-0"
                        />
                    </Tooltip>
                    <Tooltip title="Edit">
                        <Button
                            type="text"
                            icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                            onClick={() => handleEdit(record)}
                            className="p-0"
                        />
                    </Tooltip>
                </div>
            ),
        },
    ];

    // Handle table pagination change
    const handleTableChange = (newPagination) => {
        setPagination(newPagination);
        fetchAttributes(newPagination.current, newPagination.pageSize, searchText);
    };

    return (
        <div className="bg-white border border-[#DBDBDB] rounded-l-[12px] rounded-r-[12px]">
            {/* Render the Attributes component with ref but hide it */}
            <div style={{ display: "none" }}>
                <Attributes ref={attributesRef} />
            </div>

            {/* Table Controls */}
            <div className="p-5">
                <Row justify="space-between" align="middle">
                    <Col>
                        <h2 className="m-0 text-base font-semibold text-[#252525]">Variant Attributes</h2>
                    </Col>
                    <Col>
                        <div className="flex gap-2">
                            <Input
                                placeholder="Search Attribute"
                                prefix={<SearchOutlined />}
                                value={searchText}
                                onChange={handleSearch}
                                className="w-60 rounded"
                                allowClear
                                onPressEnter={onSearch}
                            />
                        </div>
                    </Col>
                </Row>
            </div>

            {/* Variant Attributes Table */}
            <div className="border-t border-[#DBDBDB]">
                <table className="w-full border-collapse">
                    <thead>
                        <tr className="bg-[#F9FAFB] border-b border-[#DBDBDB]">
                            <th className="p-4 text-left text-[#626262] font-[400] text-[14px] w-[75%] border-r border-[#DBDBDB]">Name</th>
                            <th className="p-4 text-left text-[#626262] font-[400] text-[14px] w-[15%] border-r border-[#DBDBDB]">
                                Is Required
                            </th>
                            <th className="p-4 text-left text-[#626262] font-[400] text-[14px] w-[10%]">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {loading ? (
                            <tr>
                                <td colSpan="3" className="p-4 text-center">
                                    <Spin size="default" />
                                </td>
                            </tr>
                        ) : (
                            <>
                                <tr className="border-b border-[#DBDBDB]">
                                    <td className="p-4 border-r border-[#DBDBDB]">Sku</td>
                                    <td className="p-4 border-r border-[#DBDBDB]">
                                        <Checkbox checked={true} disabled />
                                    </td>
                                    <td className="p-4">
                                        <div className="flex gap-2">
                                            <Button
                                                type="text"
                                                icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                            <Button
                                                type="text"
                                                icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr className="border-b border-[#DBDBDB]">
                                    <td className="p-4 border-r border-[#DBDBDB]">Name</td>
                                    <td className="p-4 border-r border-[#DBDBDB]">
                                        <Checkbox checked={true} disabled />
                                    </td>
                                    <td className="p-4">
                                        <div className="flex gap-2">
                                            <Button
                                                type="text"
                                                icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                            <Button
                                                type="text"
                                                icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr className="border-b border-[#DBDBDB]">
                                    <td className="p-4 border-r border-[#DBDBDB]">Barcode</td>
                                    <td className="p-4 border-r border-[#DBDBDB]">
                                        <Checkbox checked={false} disabled />
                                    </td>
                                    <td className="p-4">
                                        <div className="flex gap-2">
                                            <Button
                                                type="text"
                                                icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                            <Button
                                                type="text"
                                                icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr className="border-b border-[#DBDBDB]">
                                    <td className="p-4 border-r border-[#DBDBDB]">Weight</td>
                                    <td className="p-4 border-r border-[#DBDBDB]">
                                        <Checkbox checked={true} disabled />
                                    </td>
                                    <td className="p-4">
                                        <div className="flex gap-2">
                                            <Button
                                                type="text"
                                                icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                            <Button
                                                type="text"
                                                icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr className="border-b border-[#DBDBDB]">
                                    <td className="p-4 border-r border-[#DBDBDB]">Price</td>
                                    <td className="p-4 border-r border-[#DBDBDB]">
                                        <Checkbox checked={false} disabled />
                                    </td>
                                    <td className="p-4">
                                        <div className="flex gap-2">
                                            <Button
                                                type="text"
                                                icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                            <Button
                                                type="text"
                                                icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                        </div>
                                    </td>
                                </tr>
                                <tr className="border-b border-[#DBDBDB]">
                                    <td className="p-4 border-r border-[#DBDBDB]">Compare At Price</td>
                                    <td className="p-4 border-r border-[#DBDBDB]">
                                        <Checkbox checked={false} disabled />
                                    </td>
                                    <td className="p-4">
                                        <div className="flex gap-2">
                                            <Button
                                                type="text"
                                                icon={<img src={DeleteIcon} alt="Delete" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                            <Button
                                                type="text"
                                                icon={<img src={EditIcon} alt="Edit" className="w-[18px] h-[18px]" />}
                                                className="p-0"
                                            />
                                        </div>
                                    </td>
                                </tr>
                            </>
                        )}
                    </tbody>
                </table>
            </div>

            {/* Delete Confirmation Modal */}
            <Modal
                title="Delete Attribute"
                open={isDeleteModalVisible}
                onCancel={() => setIsDeleteModalVisible(false)}
                footer={[
                    <Button key="cancel" onClick={() => setIsDeleteModalVisible(false)}>
                        Cancel
                    </Button>,
                    <Button key="delete" type="primary" danger loading={loading} onClick={confirmDelete}>
                        Delete
                    </Button>,
                ]}
                width={400}
                centered
            >
                <p>Are you sure you want to delete this attribute?</p>
                {attributeToDelete && <p className="font-semibold">{attributeToDelete.name}</p>}
                <p>This action cannot be undone.</p>
            </Modal>
        </div>
    );
});

export default VariantAttributes;
