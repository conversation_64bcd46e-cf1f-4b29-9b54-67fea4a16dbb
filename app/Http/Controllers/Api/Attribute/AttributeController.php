<?php

namespace App\Http\Controllers\Api\Attribute;

use App\Http\Controllers\Controller;
use App\Http\Requests\Attribute\AttributeRequest;
use App\Http\Resources\AttributeResource;
use App\Models\Product\Attribute;
use Illuminate\Http\Request;
use App\Models\Product\Family;
use App\Services\AttributeService;
use Illuminate\Support\Facades\Session;
use Apimio\MappingConnectorPackage\models\Template;

class AttributeController extends Controller
{
    protected $attributeService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(AttributeService $attributeService)
    {
        $this->attributeService = $attributeService;
        $this->middleware("auth:sanctum");
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }


            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:attributes,id',
                'attribute_type_id' => 'integer|exists:attribute_types,id',
                'name' => 'string|max:255',
                'handle' => 'string|max:255',
                'is_required' => 'boolean',
                'is_default' => 'boolean',
                'paginate' => 'integer|min:1|max:255',
            ]);

            // Query to get attributes without global scopes
            $attributes = $user->attributes();

            // Apply filters if provided
            if ($request->filled("id")) {
                $attributes->where("id", $request->get("id"));
            }

            if ($request->filled("attribute_type_id")) {
                $attributes->where("attribute_type_id", $request->get("attribute_type_id"));
            }

            if ($request->filled("attribute_family_id")) {
                $attributes->with('families')->whereHas('families', function ($query) use ($request) {
                    $query->where('families.id', $request->get('attribute_family_id'));
                });
            }

            if ($request->filled("name")) {
                $attributes->where("name", "LIKE", "%" . $request->get("name") . "%");
            }

            if ($request->filled("handle")) {
                $attributes->where("handle", "LIKE", "%" . $request->get("handle") . "%");
            }

            if ($request->filled("is_required")) {
                $attributes->where("is_required", $request->get("is_required"));
            }

            if ($request->filled("is_default")) {
                $attributes->where("is_default", $request->get("is_default"));
            }

            // Determine the number of items per page
            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get the paginated data
            $paginatedAttributes = $attributes->orderBy('updated_at', 'desc')->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedAttributes->currentPage(),
                'last_page' => $paginatedAttributes->lastPage(),
                'per_page' => $paginatedAttributes->perPage(),
                'total' => $paginatedAttributes->total(),
            ];

            // Return the response with pagination details
            return $this->successResponse(
                'Attributes retrieved successfully',
                AttributeResource::collection($paginatedAttributes),
                200,
                $pagination
            );
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve attributes', $e->getCode() != 0 ? $e->getCode() : 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AttributeRequest $request)
    {
            // Create the attribute
            // $attribute = $request->user()->attributes()->create($request->validated());

            // $data = $request->validated();

            // Todo: implement following methods
            // // Set attribute rules
            // $this->attributeService->setAttributeRules($attribute, $data);

            // // Sync attribute families
            // $this->attributeService->syncAttributeFamilies($attribute, $data['attribute_family'] ?? []);

            // // Handle attribute-channel relationships
            // $this->attributeService->handleAttributeChannel($attribute, $data);

            // // Process attribute options
            // $this->attributeService->processAttributeOptions($attribute, $data['attribute_options'] ?? []);

            // // Handle Shopify metafield sync if enabled
            // if ($attribute->isShopifyMetaFieldSyncEnable($attribute)) {
            //     $this->attributeService->syncShopifyMetafield($attribute);
            // }

            // return response([
            //     'message' => 'Attribute created successfully',
            //     'attribute' => new AttributeResource($attribute),
            // ]);

            $attribute = new Attribute();
            $successMsg =   $attribute->isShopifyMetaFieldSyncEnable(true)
                ->set_data($request->all())
                    ->store(function ($error) use ($request) {
                            if (isset($request->method_type) && $request->method_type == "import") {
                                return  $error;
                            }
                            return $error;
                        }, function ($data, $isShopifyMetaFieldSyncEnable = false) use ($request) {
                            if (isset($request->method_type) && $request->method_type == "import") {

                                $sessiondata = Session::get('data');

                                $sessiondata['apimio_attributes_required']['all_families'] = Family::select('id', 'name')->whereNotIn('name', ['General', 'SEO'])->get()->toArray();

                                if ($request->method_type == "import") {
                                    //for output
                                    $sessiondata['output_array'] = Template::apimio_mapping_array();
                                }
                                Session::put('data', $sessiondata);
                                return "session data";
                            }
                            if ($data->attribute_type_id == 13) {
                                return "Variant created successfully.";
                            }
                            $successMsg = "Attribute created successfully.";
                            if ($isShopifyMetaFieldSyncEnable) {
                                $successMsg = "Success! Attribute created and syncing to Shopify. Check notifications later for status update.";
                            }
                            return ($successMsg);
                        });

            return response([
                'message' => 'Attribute created successfully '.$successMsg,
                'attribute' => new AttributeResource($attribute),
            ]);
    }




    /**
     * Display the specified resource.
     */
    public function show(Request $request ,string $id)
    {
        $attribute = $request->user()->attributes()->findOrFail($id);
        return response()->json([
            'message' => 'attribute retrieved successfully.',
            'attribute' => $attribute
        ]);

    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AttributeRequest $request, string $id)
    {
        $attribute = $request->user()->attributes()->findOrFail($id);
        $attribute->update($request->validated());
        return response()->json([
            'message' => 'attribute updated successfully',
            'attribute' =>  new AttributeResource($attribute)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request ,string $id)
    {
        $attribute = $request->user()->attributes()->findOrFail($id);
        $attribute->delete();
        return response()->json([
            'message' => 'attribute deleted successfully.',
        ]);
    }
}
